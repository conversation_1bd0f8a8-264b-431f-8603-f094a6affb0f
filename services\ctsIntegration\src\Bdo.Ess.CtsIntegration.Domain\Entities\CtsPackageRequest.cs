using Bdo.Ess.CtsIntegration.enums;
using Bdo.Ess.Shared.Constants.InformationExchanges;
using System;
using Volo.Abp.Domain.Entities.Auditing;

namespace Bdo.Ess.CtsIntegration.Entities
{
    public class CtsPackageRequest : FullAuditedEntity<Guid>
    {
        public Guid? EssInformationXmlId { get; set; }
        public string MessageRefId { get; set; } = "";
        public string? CtsPackageFileName { get; set; }
        public string ReceiverCountryCode { get; set; } = "";
        public int FiscalYear { get; set; }
        //For historical xml upload, might not able to identify exchange reason
        public ExchangeReason? ExchangeReason { get; set; }       
        public string? XmlPayload { get; set; }
        public string XmlPayloadUrl { get; set; } = "";
        public string? PackageZipUrl { get; set; }
        //Data Packet file creation date
        public DateTime? FileCreatedAt { get; set; }
        public DateTime? UploadedAt { get; set; }
        public int UploadAttempts { get; set; }
        public CTSUploadStatus? UploadStatus { get; set; }
        public string? TransmissionStatus { get; set; }
		public string? TransmissionStatusDesc { get; set; }
		public bool? EligibleCheckCtsStatus { get; set; }
        public bool IsExcludeCtsUpload { get; set; }
        public DateTime? StatusUpdatedAt { get; set; }
        public bool HasExchangeRecords { get; set; }

        public Guid TenantId { get; set; } //CA's tenantId
        public string? ProcessInfo { get; set; } //For storing process info, like error message, etc.

		public string? CtsTransmissionId { get; set; }		
		public DateTime? TransmissionStatusLastCheckedUtc { get; set; }

		public CtsPackageRequest(Guid id):base(id)
        {
        }

        public CtsPackageRequest()
        {
        }
    }
}