"use strict";(self.webpackChunkess=self.webpackChunkess||[]).push([[18508],{64939:(h,n,e)=>{e.r(n),e.d(n,{default:()=>u});const s=void 0,u=["ur",[["a","p"],["AM","PM"],s],[["AM","PM"],s,s],[["S","M","T","W","T","F","S"],["\u0627\u062a\u0648\u0627\u0631","\u067e\u06cc\u0631","\u0645\u0646\u06af\u0644","\u0628\u062f\u06be","\u062c\u0645\u0639\u0631\u0627\u062a","\u062c\u0645\u0639\u06c1","\u06c1\u0641\u062a\u06c1"],s,s],s,[["J","F","M","A","M","J","J","A","S","O","N","D"],["\u062c\u0646\u0648\u0631\u06cc","\u0641\u0631\u0648\u0631\u06cc","\u0645\u0627\u0631\u0686","\u0627\u067e\u0631\u06cc\u0644","\u0645\u0626\u06cc","\u062c\u0648\u0646","\u062c\u0648\u0644\u0627\u0626\u06cc","\u0627\u06af\u0633\u062a","\u0633\u062a\u0645\u0628\u0631","\u0627\u06a9\u062a\u0648\u0628\u0631","\u0646\u0648\u0645\u0628\u0631","\u062f\u0633\u0645\u0628\u0631"],s],s,[["\u0642\u0628\u0644 \u0645\u0633\u06cc\u062d","\u0639\u06cc\u0633\u0648\u06cc"],s,s],0,[6,0],["d/M/yy","d MMM\u060c y","d MMMM\u060c y","EEEE\u060c d MMMM\u060c y"],["h:mm a","h:mm:ss a","h:mm:ss a z","h:mm:ss a zzzz"],["{1} {0}",s,s,s],[".",",",";","%","\u200e+","\u200e-","E","\xd7","\u2030","\u221e","NaN",":"],["#,##0.###","#,##0%","\xa4\xa0#,##0.00","#E0"],"PKR","Rs","\u067e\u0627\u06a9\u0633\u062a\u0627\u0646\u06cc \u0631\u0648\u067e\u06cc\u06c1",{BYN:[s,"\u0440."],JPY:["JP\xa5","\xa5"],PHP:[s,"\u20b1"],PKR:["Rs"],THB:["\u0e3f"],TWD:["NT$"]},"rtl",function t(M){const a=Math.floor(Math.abs(M)),d=M.toString().replace(/^[^.]*\.?/,"").length;return 1===a&&0===d?1:5}]}}]);