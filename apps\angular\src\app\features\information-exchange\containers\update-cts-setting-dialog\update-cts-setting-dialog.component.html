<div mat-dialog-title>
    <div class="row">
        <div class="col-8 title">CTS Settings</div>
        <div class="col-4 text-end modal-action-button">
            <button type="button" mat-raised-button class="ui-button" (click)="onCancel()">
                <i class="fas fa-times"></i>
            </button>
        </div>
    </div>
</div>
<form [formGroup]="form" (ngSubmit)="onSubmit()">
    <mat-dialog-content>
        <div class="row">
            <div class="col-md-4">
                <mat-form-field class="w-100">
                    <mat-label>System User Name</mat-label>
                    <input matInput formControlName="systemUserName" required>
                </mat-form-field>
            </div>
            <div class="col-md-4">
                <mat-form-field class="w-100">
                    <mat-label>System User Password</mat-label>
                    <input matInput [type]="passwordVisible ? 'text' : 'password'" formControlName="systemUserPassword"
                        required>
                    <button mat-icon-button matSuffix (click)="togglePasswordVisibility()" type="button">
                        <mat-icon>{{passwordVisible ? 'visibility_off' : 'visibility'}}</mat-icon>
                    </button>
                </mat-form-field>
            </div>
        </div>
        <div class="row">
            <div *ngIf="data?.systemUserPasswordUpdatedAt" class="last-updated">
                System User Password last updated at: {{ data?.systemUserPasswordUpdatedAt | date:'dd/MM/yyyy' }}
            </div>
        </div>
        <div class="row">
            <div class="col-md-4">
                <mat-form-field class="w-100">
                    <mat-label>SFTP User Name</mat-label>
                    <input matInput formControlName="sftpUserName" required>
                </mat-form-field>
            </div>
            <div class="col-md-4">
                <mat-form-field class="w-100">
                    <mat-label>SFTP SSH Key</mat-label>
                    <input matInput formControlName="sftpSshKey">
                </mat-form-field>
            </div>
            <div class="col-md-4">
                <div class="file-upload-group">
                    <label class="file-upload-label">Upload New SSH Key File</label>
                    <div class="file-upload-row">
                        <button mat-stroked-button color="primary" type="button" (click)="fileInput.click()">
                            <mat-icon>upload_file</mat-icon>
                            Choose File
                        </button>
                        <span class="file-name" *ngIf="selectedFile; else noFile" [matTooltip]="selectedFile.name">
                            {{ selectedFile.name }}
                        </span>
                        <ng-template #noFile>
                            <span class="file-placeholder">No file chosen</span>
                        </ng-template>
                    </div>
                    <input #fileInput accept="*" type="file" formControlName="file" (change)="onFileChange($event)" class="file-input" />
                </div>
            </div>
        </div>
        <div class="row">
            <div *ngIf="data?.sftpSSHKeyUpdatedAt" class="last-updated">
                SFTP SSH Key last updated at: {{ data?.sftpSSHKeyUpdatedAt | date:'dd/MM/yyyy' }}
            </div>
        </div>
        <div class="row">
            <div class="col-md-4">
                <mat-form-field class="w-100">
                    <mat-label>CTS Public Certificate</mat-label>
                    <input matInput formControlName="ctsPublicCertificate">
                </mat-form-field>
            </div>
            <div class="col-md-4">
                <div class="file-upload-group">
                    <label class="file-upload-label">Upload New CTS Public Certificate File</label>
                    <div class="file-upload-row">
                        <button mat-stroked-button color="primary" type="button" (click)="certificateFileInput.click()">
                            <mat-icon>upload_file</mat-icon>
                            Choose File
                        </button>
                        <span class="file-name" *ngIf="selectedCertificateFile; else noCertificateFile" [matTooltip]="selectedCertificateFile.name">
                            {{ selectedCertificateFile.name }}
                        </span>
                        <ng-template #noCertificateFile>
                            <span class="file-placeholder">No file chosen</span>
                        </ng-template>
                    </div>
                    <input #certificateFileInput accept=".crt,.cer,.txt" type="file" formControlName="certificateFile" (change)="onCertificateFileChange($event)" class="file-input" />
                </div>
            </div>
        </div>
        <div class="row">
            <div *ngIf="data?.ctsPublicCertificateUpdatedAt" class="last-updated">
                CTS Public Certificate last updated at: {{ data?.ctsPublicCertificateUpdatedAt | date:'dd/MM/yyyy' }}
            </div>
        </div>
    </mat-dialog-content>
    <mat-dialog-actions align="end">        
        <button mat-raised-button type="button" (click)="onCancel()">Cancel</button>
        <button mat-raised-button class="ui-button" type="submit" [disabled]="form.invalid">Save</button>
    </mat-dialog-actions>
</form>