"use strict";(self.webpackChunkess=self.webpackChunkess||[]).push([[15022],{22839:(H,e,t)=>{t.r(e),t.d(e,{default:()=>r});const n=void 0,r=["wae",[["<PERSON>","<PERSON>"],n,n],n,[["S","M","Z","M","F","F","S"],["Sun","M\xe4n","Zi\u0161","<PERSON><PERSON>","Fr\xf3","Fri","<PERSON>"],["Sunntag","M\xe4ntag","Zi\u0161tag","<PERSON>tt<PERSON>\u010d","Fr\xf3ntag","Fritag","Sam\u0161tag"],["Sun","M\xe4n","Zi\u0161","Mit","Fr\xf3","<PERSON>i","<PERSON>"]],n,[["<PERSON>","<PERSON>","<PERSON>","<PERSON>","<PERSON>","<PERSON>","<PERSON>","\xd6","H","W","W","<PERSON>"],["<PERSON>","<PERSON><PERSON>","M\xe4r","<PERSON>br","<PERSON>","Br\xe1","Hei","\xd6ig","Her","W\xedm","Win","Chr"],["Jenner","Hornig","M\xe4rze","Abrille","Meije","Br\xe1\u010det","Heiwet","\xd6ig\u0161te","Herb\u0161tm\xe1net","W\xedm\xe1net","Winterm\xe1net","Chri\u0161tm\xe1net"]],n,[["v. Chr.","n. Chr"],n,n],1,[6,0],["y-MM-dd","d. MMM y","d. MMMM y","EEEE, d. MMMM y"],["HH:mm","HH:mm:ss","HH:mm:ss z","HH:mm:ss zzzz"],["{1} {0}",n,n,n],[",","\u2019",";","%","+","-","E","\xd7","\u2030","\u221e","NaN",":"],["#,##0.###","#,##0%","\xa4\xa0#,##0.00","#E0"],"CHF","CHF","CHF",{},"ltr",function M(i){return 1===i?1:5}]}}]);