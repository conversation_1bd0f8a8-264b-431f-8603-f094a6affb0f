﻿using Bdo.Ess.CtsIntegration.enums;
using Bdo.Ess.Shared.Constants.InformationExchanges;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;

namespace Bdo.Ess.CtsIntegration.CtsPackageRequests
{
    //CtsPackageRequestDto is already used by other UI purpose
    public class CtsPackageRequestDataDto : EntityDto<Guid>
    {
        public Guid? EssInformationXmlId { get; set; }
        public string? CtsPackageFileName { get; set; }
        public string ReceiverCountryCode { get; set; } = "";
        public int FiscalYear { get; set; }
        public ExchangeReason? ExchangeReason { get; set; }
        public string? XmlPayload { get; set; }
        public string XmlPayloadUrl { get; set; } = "";
        public string? PackageZipUrl { get; set; }
        public DateTime? FileCreatedAt { get; set; }
        public DateTime? UploadedAt { get; set; }
        public CTSUploadStatus? UploadStatus { get; set; }
        public string? TransmissionStatus { get; set; }
        public bool IsExcludeCtsUpload { get; set; }
        public DateTime? StatusUpdatedAt { get; set; }
        public bool HasExchangeRecords { get; set; }
        public Guid TenantId { get; set; }
        public string MessageRefId { get; set; } = "";
    }
}
