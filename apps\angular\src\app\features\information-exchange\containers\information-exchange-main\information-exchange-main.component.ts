import { Component, Injector, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { InformationExchangeService } from '../../../../../../proxies/proxies-informationeex-service/lib/proxy/bdo/ess/dashboard-service/controllers';
import {
  GetInformationExchangeDto,
  InformationExchangeDto,
} from '../../../../../../proxies/proxies-informationeex-service/lib/proxy/bdo/ess/dashboard-service/information-exchange-dashboard/dto';
import {
  ExchangeReason,
  InformationExchangeStatus,
} from '../../../../../../proxies/proxies-informationeex-service/lib/proxy/bdo/ess/shared/constants/information-exchanges';
import { AppComponentBase } from '../../../../app-component-base';
import { DateHelper } from '../../../../shared/utils/date-helper';
import {
  BdoTableCellLinkClickEvent,
  BdoTableCheckboxClickEvent,
  BdoTableColumnDefinition,
  BdoTableColumnType,
  BdoTableData,
  BdoTableRowActionClickEvent,
} from '../../../../shared/components/bdo-table/bdo-table.model';
import {
  ExchangeReasonDic,
  ExchangeSummaryTableColumns,
  InformationExchangeStatusDic,
  InformationExchangeTableColumns,
  Permissions,
  CTSUploadStatusDic,
  CTSUploadExchangeReasonDic,
} from '../../../../shared/constants';
import Swal from 'sweetalert2';
import { CurrentUserDto, PermissionService } from '@abp/ng.core';
import { InformationExchangeDetailsService } from '../../../../../../proxies/economic-service/lib/proxy/bdo/ess/economic-substance-service/information-exchanges';
import { MatDialog } from '@angular/material/dialog';
import { InformationExchangeHistoryComponent } from '../information-exchange-history/information-exchange-history.component';
import { CADashboardContorllerService } from 'proxies/proxies-dashboard-service/lib/proxy/bdo/ess/dashboard-service/controllers';
import { UpdateCaCertificateDialogComponent } from '../update-ca-certificate-dialog/update-ca-certificate-dialog.component';
import { CertificateService } from 'proxies/proxies-ctsintegration-service/lib/proxy/bdo/ess/cts-integration/certificate/certificate.service';
import { FileUploadService } from '../../../../shared/services/upload-file.service';
import { ToasterService } from '@abp/ng.theme.shared';
import { CountryService } from 'proxies/lookup-service/lib/proxy/bdo/ess/lookup-service/declaration';
import { CtsPackageRequestService } from 'proxies/proxies-ctsintegration-service/lib/proxy/bdo/ess/cts-integration/cts-package-request/cts-package-request.service';
import { ViewAssociatedExchangeRecordsComponent } from '../view-associated-exchange-records/view-associated-exchange-records.component';
import { UploadHistoricalXmlDialogComponent } from '../upload-historical-xml-dialog/upload-historical-xml-dialog.component';
import { DecryptDataPacketDialogComponent } from '../decrypt-data-packet-dialog/decrypt-data-packet-dialog.component';
import { RegeneratePacketDialogComponent } from '../regenerate-packet-dialog/regenerate-packet-dialog.component';
import { ViewCommentDialogComponent } from '../view-comment-dialog/view-comment-dialog.component';
import { CTSUploadStatus } from 'proxies/proxies-ctsintegration-service/lib/proxy/bdo/ess/cts-integration/enums/ctsupload-status.enum';
import { GetCtsPackageRequestDto } from 'proxies/proxies-ctsintegration-service/lib/proxy/bdo/ess/cts-integration/application/contracts/cts-package-requests';
import { SweetAlertService } from '@app/shared/services/sweetalert.service';
import { UploadCtsDialogComponent } from '../upload-cts-dialog/upload-cts-dialog.component';
import { UpdateCtsSettingDialogComponent } from '../update-cts-setting-dialog/update-cts-setting-dialog.component';
import { BahamasCtsSettingDto, BahamasCtsSettingService } from 'proxies/proxies-ctsintegration-service/lib/proxy/bdo/ess/cts-integration/bahamas-cts-settings';

@Component({
  selector: 'app-information-exchange-main',
  templateUrl: './information-exchange-main.component.html',
  styleUrls: ['./information-exchange-main.component.scss']
})
export class InformationExchangeMainComponent
  extends AppComponentBase
  implements OnInit {
  input: GetInformationExchangeDto = {
    maxResultCount: 10,
    skipCount: 0,
    sorting: 'ExchangeReason asc',
    informationExchangeStatus: InformationExchangeStatus.None,
    entityName: '',
    year: '',
  };
  TableId = 'information_ex-results';
  /* Work for pagination. Default value = 0, it is rendering first page by default. */
  currentPageIndex = 0;
  InformationExchange: InformationExchangeDto[];
  /** It is string year number array. */
  year = [];
  /** Selected year from Financial Period End Years dropdown, default is current year. */
  selectedYear = new Date().getFullYear().toString(); //currnt year number value as string by default.
  exchangeResultColumns: BdoTableColumnDefinition[] = [
    {
      columnId: InformationExchangeTableColumns.EXCHANGE_REASON,
      type: BdoTableColumnType.String,
      minWidth: 120,
      frozenLeft: true,
      isSortable: true,
      columnName: 'Exchange Reason',
    },
    {
      columnId: InformationExchangeTableColumns.RA_CODE,
      type: BdoTableColumnType.String,
      minWidth: 60,
      isSortable: true,
      columnName: 'RA Name',
    },
    {
      columnId: InformationExchangeTableColumns.ENTITY_NAME,
      type: BdoTableColumnType.String,
      minWidth: 100,
      isSortable: true,
      columnName: 'Entity Name',
    },
    {
      columnId: InformationExchangeTableColumns.INCROP_NUMBER,
      type: BdoTableColumnType.String,
      minWidth: 60,
      isSortable: true,
      columnName: 'Incop#/Formation#',
    },
    {
      columnId: InformationExchangeTableColumns.FINANCIAL_PERIOD,
      type: BdoTableColumnType.Date,
      minWidth: 60,
      isSortable: true,
      columnName: 'Financial Period End Date',
    },
    {
      columnId: InformationExchangeTableColumns.DUE_DATE,
      type: BdoTableColumnType.Date,
      minWidth: 60,
      isSortable: true,
      columnName: 'Due Date',
    },
    {
      columnId: InformationExchangeTableColumns.INFORMATIONEXCH_STATUS,
      type: BdoTableColumnType.String,
      minWidth: 60,
      isSortable: true,
      columnName: 'Information Exchange Status',
    },
    {
      columnId: InformationExchangeTableColumns.VIEW_DECLARATION,
      type: BdoTableColumnType.Link,
      minWidth: 60,
      columnName: 'View Declaration',
    },
    {
      columnId: InformationExchangeTableColumns.XML_DATA,
      type: BdoTableColumnType.Link,
      minWidth: 60,
      columnName: 'XML Data',
    },
    {
      columnId: InformationExchangeTableColumns.VIEW_HISTORY,
      type: BdoTableColumnType.Link,
      minWidth: 60,
      columnName: 'View History',
    },
  ];
  /** Page size setting for "TableId" grid */
  PageSize = 10;
  exchangeInformationResultRecords = [];
  selectReportStatus: number = InformationExchangeStatus.None;
  searchEntityName: any;
  informationExchangedDic: any = InformationExchangeStatusDic;
  TableIdS = 'information_ex_summary';
  currentPageIndexS = 0;
  summaryExchangeColumns: BdoTableColumnDefinition[] = [
    {
      columnId: ExchangeSummaryTableColumns.TOTAL_REPORT,
      type: BdoTableColumnType.Number,
      minWidth: 120,
      frozenLeft: true,
      isSortable: false,
      columnName: 'Total # of Reports',
    },
    {
      columnId: ExchangeSummaryTableColumns.TOTAL_RPOERT_SENT,
      type: BdoTableColumnType.Number,
      minWidth: 120,
      frozenLeft: true,
      isSortable: false,
      columnName: 'Total # of Reports XML Generated',
    },
    {
      columnId: ExchangeSummaryTableColumns.TOTAL_RPOERT_READY,
      type: BdoTableColumnType.Number,
      minWidth: 120,
      frozenLeft: true,
      isSortable: false,
      columnName: 'Total # of Reports Ready for Exchange',
    },
    {
      columnId: ExchangeSummaryTableColumns.TOTAL_RPOERT_REVIEW,
      type: BdoTableColumnType.Number,
      minWidth: 200,
      frozenLeft: true,
      isSortable: false,
      columnName:
        'Total # of Reports for Review<br>(Include: Not required, Waiting for Review/Appeal)',
    },
    {
      columnId: ExchangeSummaryTableColumns.TOTAL_RPOERT_NOT_SENT,
      type: BdoTableColumnType.Number,
      minWidth: 120,
      frozenLeft: true,
      isSortable: false,
      columnName: 'Total # of Reports Not Started',
    },
  ];
  /** Page Size setting for "TableIdS" grid. Note: It is not the same as PageSize variable. Don't confuse. */
  PageSizeS = 10;
  totalRecords = 0;
  /** Note: Only logon user with permission "Generate XML"
   * is able to see the "Non-compliance XML","High Risk IP XML","Non-resident XML" buttons. */
  showButton = true;
  showOtherCase = true;
  standardMonitoringYear: any;
  /* Default current year value as string. */
  currnetYear = new Date().getFullYear().toString();

  certificateExpirationDate: string | null = null;
  bahamasCtsSetting: BahamasCtsSettingDto | null = null;
  isCaSystemAdmin: boolean = false;

  // Dashboard columns for CTS Upload & Transmission
  ctsDashboardColumns: BdoTableColumnDefinition[] = [
    {
      columnId: 'totalNotUploaded',
      type: BdoTableColumnType.Number,
      minWidth: 120,
      frozenLeft: true,
      isSortable: false,
      columnName: '# of Packets Do Not Upload',
    },
    {
      columnId: 'totalReadyForUpload',
      type: BdoTableColumnType.Number,
      minWidth: 120,
      frozenLeft: true,
      isSortable: false,
      columnName: '# of Packets Ready For Upload',
    },
    {
      columnId: 'totalFailedUpload',
      type: BdoTableColumnType.Number,
      minWidth: 120,
      frozenLeft: true,
      isSortable: false,
      columnName: '# of Packets Failed in Upload',
    },
    {
      columnId: 'totalUploadedToCTS',
      type: BdoTableColumnType.Number,
      minWidth: 120,
      frozenLeft: true,
      isSortable: false,
      columnName: '# of Packets Uploaded to CTS',
    },
    {
      columnId: 'totalNotEnrolled',
      type: BdoTableColumnType.Number,
      minWidth: 120,
      frozenLeft: true,
      isSortable: false,
      columnName: '# of Packets Receiving Country Not Enrolled',
    },
  ];

  // Dashboard data for CTS Upload & Transmission
  ctsDashboardList: any[] = [
    {
      id: 1,
      totalNotUploaded: 0,
      totalReadyForUpload: 1,
      totalFailedUpload: 1,
      totalUploadedToCTS: 2,
      totalNotEnrolled: 2,
    },
  ];

  // Grid columns for CTS Upload & Transmission
  ctsUploadColumns: BdoTableColumnDefinition[] = [
    {
      columnId: 'exchangeReason',
      type: BdoTableColumnType.String,
      minWidth: 120,
      frozenLeft: true,
      isSortable: true,
      columnName: 'Exchange Reason',
    },
    {
      columnId: 'dataPacket',
      type: BdoTableColumnType.Link,
      minWidth: 120,
      isSortable: false,
      columnName: 'Data Packet',
    },
    {
      columnId: 'fileCreationDate',
      type: BdoTableColumnType.Date,
      minWidth: 120,
      isSortable: true,
      columnName: 'File Creation Date',
    },
    {
      columnId: 'receivingCountry',
      type: BdoTableColumnType.String,
      minWidth: 120,
      isSortable: false,
      columnName: 'Receiving Country',
    },
    {
      columnId: 'ctsUploadStatus',
      type: BdoTableColumnType.String,
      minWidth: 120,
      isSortable: false,
      columnName: 'CTS Upload Status',
    },
    {
      columnId: 'uploadedAt',
      type: BdoTableColumnType.DateTime,
      minWidth: 120,
      isSortable: true,
      columnName: 'Uploaded At',
    },
    {
      columnId: 'ctsTransmissionStatus',
      type: BdoTableColumnType.String,
      minWidth: 120,
      isSortable: false,
      columnName: 'CTS Transmission Status',
    },
    {
      columnId: 'viewExchangeRecords',
      type: BdoTableColumnType.Link,
      minWidth: 60,
      isSortable: false,
      columnName: 'View Exchange Records',
    },
    {
      columnId: 'viewComments',
      type: BdoTableColumnType.Link,
      minWidth: 60,
      isSortable: false,
      columnName: 'View Comments',
    },
    {
      columnId: 'regeneratePacket',
      type: BdoTableColumnType.SingleActionButton,
      minWidth: 60,
      isSortable: false,
      columnName: 'Regenerate Packet',
    },
    {
      columnId: 'ctsUpload',
      type: BdoTableColumnType.SingleActionButton,
      minWidth: 60,
      isSortable: false,
      columnName: 'CTS Upload',
    },
    {
      columnId: 'excludeFromCtsUpload',
      type: BdoTableColumnType.Checkbox,
      minWidth: 60,
      isSortable: false,
      columnName: 'Exclude From CTS Upload',
    },
  ];

  // CTS Upload & Transmission Dashboard
  ctsUploadExchangeReasonDic: any = CTSUploadExchangeReasonDic;
  ctsUploadStatusDic: any = CTSUploadStatusDic;
  countries: any;
  uploadHistoricalCountries: any;
  ctsUploadSelectedYear = new Date().getFullYear().toString(); //currnt year number value as string by default. 
  ctsUploadResultRecords = [];
  selectExchangeReason: number = -1;
  selectCtsUploadStatus: number = CTSUploadStatus.NotStarted;
  selectReceivingCountry: string = '';

  // Table IDs and page settings
  ctsDashboardTableId = 'cts_dashboard';
  ctsUploadTableId = 'cts_upload_grid';
  ctsUploadPageSize = 10;
  ctsUploadCurrentPage = 0;
  ctsUploadTotalRecords = 0;
  ctsUploadInput: GetCtsPackageRequestDto = {
    maxResultCount: 10,
    skipCount: 0,
    sorting: 'ExchangeReason asc',
    ctsUploadStatus: null,
    exchangeReason: null,
    financialEndYear: '',
    receivingCountry: '',
  };
  showBahamasSettings: boolean = false;
  showDataPacketDashboard: boolean = false;
  showUpdateCACertificate: boolean = false;
  showCTSUpload: boolean = false;
  showRefreshStatus: boolean = false;
  showRegenerateDataPacket: boolean = false;
  showDecryptReceivedDataPacket: boolean = false;
  showUploadHistoricalXml: boolean = false;

  constructor(
    injector: Injector,
    private router: Router,
    private informationExchangeService: InformationExchangeService,
    private informationExchangeDetailService: InformationExchangeDetailsService,
    private permissionService: PermissionService,
    private dashboardService: CADashboardContorllerService,
    public dialog: MatDialog,
    private certificateService: CertificateService,
    private fileUploadService: FileUploadService,
    private toasterService: ToasterService,
    private countryService: CountryService,
    private ctsPackageRequestService: CtsPackageRequestService,
    private sweetAlert: SweetAlertService,
    private bahamasCtsSettingService: BahamasCtsSettingService,
  ) {
    super(injector);
  }

  ngOnInit(): void {
    this.getFiscalYears().subscribe((response) => {
      if (response && response.length > 0) {
        this.year = [];
        response.forEach((element) => {
          this.year.push(element.toString());
        });
      };
    });

    if (localStorage.getItem('selectedYear')) {
      this.selectedYear =
        localStorage.getItem('selectedYear') ?? this.currnetYear;
    }

    if (localStorage.getItem('selectReportStatus')) {
      this.selectReportStatus = Number(
        localStorage.getItem('selectReportStatus')
      );
    }
    this.informationExchangeDetailService
      .standardMonitoringFromYear()
      .subscribe((response) => {
        this.standardMonitoringYear = response;

        this.IsShowOtherCase(this.selectedYear);
      });

    this.onLazyLoadEvent(undefined);
    this.onLazyLoadEventS(undefined);

    this.showButton = this.checkUserPermission();

    // CTS Upload & Transmission Dashboard
    // Check CA System Admin role
    const currentUser = this.configState.getOne('currentUser') as CurrentUserDto;
    this.isCaSystemAdmin = !!currentUser?.roles?.includes('CA System Admin');

    if (localStorage.getItem('ctsUploadSelectedYear')) {
      this.ctsUploadSelectedYear =
        localStorage.getItem('ctsUploadSelectedYear') ?? this.currnetYear;
    }

    if (localStorage.getItem('selectExchangeReason')) {
      this.selectExchangeReason = Number(
        localStorage.getItem('selectExchangeReason')
      );
    }

    if (localStorage.getItem('selectCtsUploadStatus')) {
      this.selectCtsUploadStatus = Number(
        localStorage.getItem('selectCtsUploadStatus')
      );
    }

    if (localStorage.getItem('selectReceivingCountry')) {
      this.selectReceivingCountry = localStorage.getItem('selectReceivingCountry');
    }

    this.showBahamasSettings = this.permissionService.getGrantedPolicy(Permissions.BAHAMAS_CTS_SETTING);
    this.showDataPacketDashboard = this.permissionService.getGrantedPolicy(Permissions.DATA_PACKET_DASHBOARD);
    this.showUpdateCACertificate = this.permissionService.getGrantedPolicy(Permissions.UPDATE_CA_CERTIFICATE);
    this.showCTSUpload = this.permissionService.getGrantedPolicy(Permissions.CTS_UPLOAD);
    this.showRefreshStatus = this.permissionService.getGrantedPolicy(Permissions.REFRESH_STATUS);
    this.showRegenerateDataPacket = this.permissionService.getGrantedPolicy(Permissions.REGENERATE_DATA_PACKET);
    this.showDecryptReceivedDataPacket = this.permissionService.getGrantedPolicy(Permissions.DECRYPT_RECEIVED_DATA_PACKET);
    this.showUploadHistoricalXml = this.permissionService.getGrantedPolicy(Permissions.UPLOAD_HISTORICAL_XML);

    this.getCountries();
    if (this.showUpdateCACertificate) {
      this.getBahamasCertificateInfo();
    }
    if (this.showBahamasSettings) {
      this.getBahamasCtsSettingInfo();
    }
    if (this.showDataPacketDashboard) {
      this.onCtsUploadLazyLoadEvent(undefined);
      this.onCtsDashboardLazyLoadEvent(undefined);
    }
  }

  IsShowOtherCase(year: string) {
    const selectedYearAsInt: number = parseInt(year, 10);
    const standardMonitoringYearAsInt: number = parseInt(
      this.standardMonitoringYear,
      10
    );
    this.showOtherCase =
      selectedYearAsInt <= standardMonitoringYearAsInt ? true : false;
  }

  /** Lazy load event works for "TableIds" grid only. */
  onLazyLoadEventS(event): void {
    this.currentPageIndexS = 0;
    this.informationExchangeService
      .getSummaryByYearByYear(this.selectedYear)
      .subscribe((response) => {
        this.summaryExchangeList = response;
        setTimeout(() => {
          this.setTableDataS();
        }, 200);
      });
  }

  /** Lazy load event works for grid "TableId" only. */
  onLazyLoadEvent(event): void {
    if (event) {
      if (this.PageSize === (event.pageSize ?? 10)) {
        this.currentPageIndex = event.pageNumber ?? 0;
      } else {
        //
        // if Page size got changed through pagination control,
        // need to reset current page index to 0.
        //
        this.PageSize = event.pageSize ?? 10;
        this.currentPageIndex = 0;
      }

      this.input.skipCount =
        (this.currentPageIndex ?? 0) * (this.PageSize ?? 10);

      this.input.maxResultCount = this.PageSize ?? 10;

      if (event.isAscending === false) {
        this.input.sorting = `${event.sortField} desc`;
      } else {
        this.input.sorting = `${event.sortField} asc`;
      }
    } else {
      this.currentPageIndex = 0;
      this.PageSize = 10;
      this.input.informationExchangeStatus = this.selectReportStatus;
      this.input.year = this.selectedYear;
      this.input.skipCount = 0;
      this.input.maxResultCount = this.PageSize;
    }

    this.informationExchangeService
      .getALLInformationExchangeByInput(this.input)
      .subscribe((response) => {
        if (response) {
          this.totalRecords = response.totalCount;
          this.exchangeInformationResultRecords = response.items;
        } else {
          this.totalRecords = 0;
          this.exchangeInformationResultRecords = [];
        }

        setTimeout(() => {
          this.setTableData();
        }, 200);
      });
  }

  summaryExchangeList: any[] = [
    {
      id: 1,
      totalNoReport: 100,
      totalNoReportSent: 10,
      totalNoReportRExchange: 5,
      totalNoReportRReview: 2,
      totalNoReportNotSent: 5,
    },
  ];

  setTableDataS(): void {
    const tableData = new BdoTableData();
    tableData.resetToFirstPage = false;
    tableData.tableId = this.TableIdS;
    tableData.totalRecords = 1;
    tableData.data = this.summaryExchangeList.map((x) => {
      return {
        id: x.id,
        rawData: x,
        cells: [
          {
            columnId: ExchangeSummaryTableColumns.TOTAL_REPORT,
            value: x.totalNoofReports,
          },
          {
            columnId: ExchangeSummaryTableColumns.TOTAL_RPOERT_SENT,
            value: x.totalNoofExchangedReports,
          },
          {
            columnId: ExchangeSummaryTableColumns.TOTAL_RPOERT_READY,
            value: x.totalNoofReadyExchangedReports,
          },
          {
            columnId: ExchangeSummaryTableColumns.TOTAL_RPOERT_REVIEW,
            value: x.totalNoofReviewReports,
          },
          {
            columnId: ExchangeSummaryTableColumns.TOTAL_RPOERT_NOT_SENT,
            value: x.totalNotSentReports,
          },
        ],
      };
    });
    setTimeout(() => {
      this.tableService.setGridData(tableData);
    }, 10);
  }

  getExchangeReasonDescription(input: any) {
    const foundStatus = ExchangeReasonDic.find(
      (status) => status.value === input
    );
    if (foundStatus) return foundStatus.description;
    return '';
  }

  getInformationExchangeStatusDescription(input: any) {
    const foundStatus = InformationExchangeStatusDic.find(
      (status) => status.value === input
    );
    if (foundStatus) return foundStatus.description;
    return '';
  }

  base64ToUint8Array(x: string) {
    const raw = atob(x);
    var rawLength = raw.length;
    var array = new Uint8Array(new ArrayBuffer(rawLength));

    for (let i = 0; i < rawLength; i++) {
      array[i] = raw.charCodeAt(i);
    }
    return array;
  }

  downloadFile(content: string, name: string) {
    var file = new Blob([this.base64ToUint8Array(content)]);
    var fileURL = window.URL.createObjectURL(file);

    var element = document.createElement('a');
    document.body.appendChild(element);
    element.style.display = 'none';
    element.href = fileURL;
    element.download = name;
    element.click();
    element.remove();
  }
 

  GenerateXMlByType(exchangeType: ExchangeReason) {
    this.informationExchangeService
      .getXMLFilesFilterByExchangeTypeByReasonAndYear(
        exchangeType,
        this.selectedYear
      )
      .subscribe((result) => {
        this.onLazyLoadEvent(undefined);
        if (result.fileName != '') {
          this.downloadFile(result.content.toString(), result.fileName);
        } else
          Swal.fire({
            icon: 'info',
            title: 'XML Import',
            text: 'No data to export.',
            allowOutsideClick: false,
          });
      });
  }

  tabChanged(event: any) {
    if (event.index === 1) {
      if (this.showDataPacketDashboard) {
        this.onCtsUploadLazyLoadEvent(undefined);
        this.onCtsDashboardLazyLoadEvent(undefined);
      }
    }
  }

  setTableData(): void {
    const tableData = new BdoTableData();
    tableData.resetToFirstPage = false;
    tableData.tableId = this.TableId;
    tableData.totalRecords = this.totalRecords;
    tableData.data = this.exchangeInformationResultRecords.map((x) => {
      return {
        id: x.id,
        rawData: x,
        cells: [
          {
            columnId: InformationExchangeTableColumns.EXCHANGE_REASON,
            value: this.getExchangeReasonDescription(x.exchangeReason),
          },
          {
            columnId: InformationExchangeTableColumns.RA_CODE,
            value: x.raCode,
          },
          {
            columnId: InformationExchangeTableColumns.ENTITY_NAME,
            value: x.entityName,
          },
          {
            columnId: InformationExchangeTableColumns.INCROP_NUMBER,
            value: x.companyFormationNumber,
          },
          {
            columnId: InformationExchangeTableColumns.FINANCIAL_PERIOD,
            value: x.fiscalEndDate,
          },
          {
            columnId: InformationExchangeTableColumns.DUE_DATE,
            value: x.dueDate,
          },
          {
            columnId: InformationExchangeTableColumns.INFORMATIONEXCH_STATUS,
            value: this.getInformationExchangeStatusDescription(
              x.informationExchangeStatus
            ),
          },
          {
            columnId: InformationExchangeTableColumns.VIEW_DECLARATION,
            /** If the underneath data "IsMigrated" flag is true, then disable the link, otherwise enable the link to view declaration page */
            value: x.isMigrated === false ? 'view' : '',
          },
          {
            columnId: InformationExchangeTableColumns.XML_DATA,
            value: 'XML Data',
          },
          {
            columnId: InformationExchangeTableColumns.VIEW_HISTORY,
            value: x.hasHistoryRecord ? 'View History' : '',
          },
        ],
      };
    });
    setTimeout(() => {
      this.tableService.setGridData(tableData);
    }, 100);
  }

  onYearChange(ob) {
    this.selectedYear = ob.value;
    this.IsShowOtherCase(this.selectedYear);
    // Keep the selected Year in local storage.
    localStorage.setItem('selectedYear', ob.value);

    this.input.informationExchangeStatus = this.selectReportStatus;
    this.input.year = this.selectedYear;
    this.input.entityName = this.searchEntityName;
    this.input.skipCount = 0;
    this.input.maxResultCount = this.PageSize;
    this.informationExchangeService
      .getALLInformationExchangeByInput(this.input)
      .subscribe((response) => {
        this.totalRecords = response.totalCount;
        this.exchangeInformationResultRecords = response.items;

        setTimeout(() => {
          this.setTableData();
        }, 200);
      });

    this.onLazyLoadEventS(undefined);
  }

  onSearch() {
    this.input.informationExchangeStatus = this.selectReportStatus;
    this.input.year = this.selectedYear;
    this.input.entityName = this.searchEntityName;
    this.input.skipCount = 0;
    this.input.maxResultCount = this.PageSize;
    this.informationExchangeService
      .getALLInformationExchangeByInput(this.input)
      .subscribe((response) => {
        this.totalRecords = response.totalCount;
        this.exchangeInformationResultRecords = response.items;

        setTimeout(() => {
          this.setTableData();
        }, 200);
      });

    this.onLazyLoadEventS(undefined);
  }

  onReportChange(ob) {
    this.selectReportStatus = Number(ob.value);
    this.input.informationExchangeStatus = this.selectReportStatus;
    this.input.year = this.selectedYear;
    this.input.entityName = this.searchEntityName;
    this.input.skipCount = 0;
    this.input.maxResultCount = this.PageSize;

    // Keep the selected Report Status in local storage.
    localStorage.setItem('selectReportStatus', ob.value);

    this.informationExchangeService
      .getALLInformationExchangeByInput(this.input)
      .subscribe((response) => {
        this.totalRecords = response.totalCount;
        this.exchangeInformationResultRecords = response.items;

        setTimeout(() => {
          this.setTableData();
        }, 200);
      });
  }

  onLinkClick(event: BdoTableCellLinkClickEvent) {
    const data = event.rawData as InformationExchangeDto;
    if (event.columnId === InformationExchangeTableColumns.XML_DATA) {
      //
      // Note: /es-info-exchange/exchangedetail page is shared with "XML Data" view which parameter "id" = "Id" of table dbo.InformationExchanges,
      // and "Information Exchange History Page" view, which paramter "id" = "InformationExchangeDetailId" of table dbo.InformationExchangeHistories.
      //
      this.router.navigate(['/es-info-exchange/exchangedetail'], {
        //
        // Passed "Id" of table dbo.InformationExchanges.
        //
        queryParams: { id: data.id, ishistory: false },
      });
    } else if (
      event.columnId === InformationExchangeTableColumns.VIEW_DECLARATION
    ) {
      //
      // When click the "view" link button in the Information Exchange records grid.
      // Route to CaActionPageComponent.ts component
      //
      this.router.navigate(['/action-page'], {
        queryParams: {
          declarationid: data.declarationId,
          entityid: data.corporateEntityId,
          from: 'info-exchange',
        },
      });
    } else if (
      event.columnId === InformationExchangeTableColumns.VIEW_HISTORY
    ) {
      //
      // Open dialog to show history records. informantion-exchange-history.component.ts
      //
      this.openInformationExchangeHistoryDialog(data.id);
    }
  }

  openInformationExchangeHistoryDialog(informationExchangeId: string) {
    const dialogRef = this.dialog.open(InformationExchangeHistoryComponent, {
      height: '750px',
      width: '1200px',
      data: { informationExchangeId: informationExchangeId },
    });

    dialogRef.afterClosed().subscribe((result) => {
      console.log('The dialog was closed', result);
    });
  }

  /** Check if logon user has permission "Generate XML".
   *  Work for show/hide three xml buttons.
   */
  checkUserPermission() {
    let result = false;
    // Get current logon user object.
    const currentUser = this.configState.getOne(
      'currentUser'
    ) as CurrentUserDto;

    if (currentUser) {
      result = this.permissionService.getGrantedPolicy(
        Permissions.DASHBOARD_GENERATE_XML
      );
    }

    return result;
  }

  /** Call backend to get fiscal years collection from table dbo.FiscalYears in database ES_Dashboard. */
  getFiscalYears() {
    return this.dashboardService.getFiscalYears().pipe();
  }


  // CTS Upload & Transmission Dashboard Methods
  getCTSUploadStatusDescription(input: any) {
    const foundStatus = CTSUploadStatusDic.find(
      (status) => status.value === input
    );
    if (foundStatus) return foundStatus.description;
    return '';
  }

  getReceivingCountryName(input: any) {
    const foundCountry = this.countries.find(
      (status) => status.code2 === input
    );
    if (foundCountry) return foundCountry.name;
    return '';
  }

  getCountries() {
    this.countryService.getList({ sorting: "name asc", maxResultCount: 1000 }).subscribe(response => {
      this.countries = response.items;
      this.uploadHistoricalCountries = response.items;
      // Remove code2 with empty string and null values in countries      
      this.countries = this.countries.filter(country => country.code2 && country.code2.trim() !== '');
      this.uploadHistoricalCountries = this.uploadHistoricalCountries.filter(country => country.code2 && country.code2.trim() !== '');
      // add new country ALL in countries 
      this.countries.unshift({ name: 'All', code2: '' });
    });
  }

  getBahamasCertificateInfo() {
    return this.certificateService.getBahamasCertificateInfo().subscribe({
      next: (info) => {
        this.certificateExpirationDate = info?.expiredAt || null;
      },
      error: () => {
        this.certificateExpirationDate = null;
      }
    });
  }

  getBahamasCtsSettingInfo() {
    return this.bahamasCtsSettingService.getCurrentSettings().subscribe({
      next: (info) => {
        this.bahamasCtsSetting = info || null;
      },
      error: () => {
        this.bahamasCtsSetting = null;
      }
    });
  }

  openUpdateCtsSettingDialog() {
    const dialogRef = this.dialog.open(UpdateCtsSettingDialogComponent, {
      width: '1200px',
      data: this.bahamasCtsSetting || null
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (!result) return;

      const formData: FormData = new FormData();
      if (result.file) {
        formData.append('fileName', result.file.name);
        formData.append('file', result.file);
        formData.append('fileType', result.file.type);
      }

      const certificateFileFormData: FormData = new FormData();
      if (result.certificateFile) {
        certificateFileFormData.append('fileName', result.certificateFile.name);
        certificateFileFormData.append('file', result.certificateFile);
        certificateFileFormData.append('fileType', result.certificateFile.type);
      }

      const isCreating = result.id == null;
      const serviceCall = isCreating
        ? this.fileUploadService.createBahamasCtsSettings(result, formData, certificateFileFormData)
        : this.fileUploadService.updateBahamasCtsSettings(result, formData, certificateFileFormData);

      const action = isCreating ? 'create' : 'update';

      serviceCall.subscribe({
        next: (response) => {
          if (response) {
            this.getBahamasCtsSettingInfo();
            this.toasterService.success(`CTS Settings successfully ${action}d.`, '', { life: 5000 });
          } else {
            this.toasterService.warn(`CTS Settings could not be ${action}d. Please try again later.`, null, { life: 7000 });
          }
        },
        error: (error) => {
          this.toasterService.error(`An error occurred while trying to ${action} CTS Settings.`, null, { life: 5000 });
          console.error(`Error ${action}ing CTS Settings:`, error);
        }
      });
    });
  }

  openUpdateCaCertificateDialog() {
    const dialogRef = this.dialog.open(UpdateCaCertificateDialogComponent, {
      width: '500px',
      data: {},
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result && result.file) {
        const formData: FormData = new FormData();
        formData.append('fileName', result.file.name);
        formData.append('file', result.file);
        formData.append('fileType', result.file.type);
        // Only call upload if file is present
        this.fileUploadService
          .uploadBahamasCertificate(formData, result.password).subscribe({
            next: (response) => {
              if (response) {
                // Fetch certificate expiration date
                this.getBahamasCertificateInfo();
                this.toasterService.success('Bahamas Certificate successfully uploaded', '', { life: 5000 });
              }
              else {
                this.toasterService.warn('Bahamas Certificate couldn’t be uploaded. Please try again later', null, { life: 7000 });
              }
            },
            error: (error) => {
              //this.toasterService.error(error?.error?.error?.message, null, { life: 5000 });
              console.error('Error uploading Bahamas certificate:', error);
            }
          })
      }
    });
  }

  // CTS Upload & Transmission Dashboard Methods
  setCtsDashboardTableData(): void {
    const tableData = new BdoTableData();
    tableData.resetToFirstPage = false;
    tableData.tableId = this.ctsDashboardTableId;
    tableData.totalRecords = 1;
    tableData.data = this.ctsDashboardList.map(x => ({
      id: x.id,
      rawData: x,
      cells: [
        { columnId: 'totalNotUploaded', value: x.totalNotUploaded },
        { columnId: 'totalReadyForUpload', value: x.totalReadyForUpload },
        { columnId: 'totalFailedUpload', value: x.totalFailedUpload },
        { columnId: 'totalUploadedToCTS', value: x.totalUploadedToCTS },
        { columnId: 'totalNotEnrolled', value: x.totalNotEnrolled },
      ],
    }));
    setTimeout(() => this.tableService.setGridData(tableData), 10);
  }

  setCtsUploadTableData(): void {
    const tableData = new BdoTableData();
    tableData.resetToFirstPage = false;
    tableData.tableId = this.ctsUploadTableId;
    tableData.totalRecords = this.ctsUploadTotalRecords;
    tableData.data = this.ctsUploadResultRecords.map(x => ({
      id: x.id,
      rawData: x,
      cells: [
        { columnId: 'exchangeReason', value: this.getExchangeReasonDescription(x.exchangeReason) },
        { columnId: 'dataPacket', value: x.dataPacket },
        { columnId: 'fileCreationDate', value: DateHelper.formatUtcToLocalDate(x.fileCreationDate) },
        { columnId: 'receivingCountry', value: this.getReceivingCountryName(x.receivingCountry) },
        { columnId: 'ctsUploadStatus', value: this.getCTSUploadStatusDescription(x.ctsUploadStatus) },
        { columnId: 'uploadedAt', value: DateHelper.formatUtcToLocalDate(x.uploadedAt) },
        { columnId: 'ctsTransmissionStatus', value: x.ctsTransmissionStatus },
        { columnId: 'viewExchangeRecords', value: x.viewExchangeRecords === true ? 'View' : null },
        { columnId: 'viewComments', value: x.viewComments.length > 0 ? 'View Comments' : null },
        {
          columnId: 'regeneratePacket',
          value: this.showRegenerateDataPacket && x.allowedActions?.includes(0)
            ? { actionType: 'RegenerateDataPacket', icon: 'refresh', tooltip: "Regenerate Packet" }
            : null
        },
        {
          columnId: 'ctsUpload',
          value: this.showCTSUpload && x.allowedActions?.includes(1)
            ? { actionType: 'ctsUpload', icon: 'cloud_upload', tooltip: "CTS Upload" }
            : null
        },
        {
          columnId: 'excludeFromCtsUpload',
          hide: x.allowedActions?.includes(2) || x.allowedActions?.includes(3) ? false : true,
          value: x.excludeFromCtsUpload
        }
      ],
    }));

    setTimeout(() => this.tableService.setGridData(tableData), 100);
  }

  onCtsDashboardLazyLoadEvent(event): void {
    this.currentPageIndexS = 0;
    this.ctsPackageRequestService
      .getSummaryByYearByYear(this.ctsUploadSelectedYear)
      .subscribe((response) => {
        this.ctsDashboardList = response;
        setTimeout(() => {
          this.setCtsDashboardTableData();
        }, 200);
      });
  }

  onCtsUploadLazyLoadEvent(event): void {
    if (event) {
      if (this.ctsUploadPageSize === (event.pageSize ?? 10)) {
        this.ctsUploadCurrentPage = event.pageNumber ?? 0;
      } else {
        //
        // if Page size got changed through pagination control,
        // need to reset current page index to 0.
        //
        this.ctsUploadPageSize = event.pageSize ?? 10;
        this.ctsUploadCurrentPage = 0;
      }

      this.ctsUploadInput.skipCount =
        (this.ctsUploadCurrentPage ?? 0) * (this.ctsUploadPageSize ?? 10);

      this.ctsUploadInput.maxResultCount = this.ctsUploadPageSize ?? 10;

      if (event.isAscending === false) {
        this.ctsUploadInput.sorting = `${event.sortField} desc`;
      } else {
        this.ctsUploadInput.sorting = `${event.sortField} asc`;
      }
    } else {
      this.ctsUploadCurrentPage = 0;
      this.ctsUploadPageSize = 10;
      this.ctsUploadInput.financialEndYear = this.ctsUploadSelectedYear;
      this.ctsUploadInput.exchangeReason = this.selectExchangeReason !== -1 ? this.selectExchangeReason : null;
      this.ctsUploadInput.ctsUploadStatus = this.selectCtsUploadStatus !== -1 ? this.selectCtsUploadStatus : null;
      this.ctsUploadInput.receivingCountry = this.selectReceivingCountry ? this.selectReceivingCountry : null;
      this.ctsUploadInput.skipCount = 0;
      this.ctsUploadInput.maxResultCount = this.ctsUploadPageSize;
    }

    this.ctsPackageRequestService
      .getAllCtsPackageRequestByInput(this.ctsUploadInput)
      .subscribe((response) => {
        if (response) {
          this.ctsUploadTotalRecords = response.totalCount;
          this.ctsUploadResultRecords = response.items;
        } else {
          this.ctsUploadTotalRecords = 0;
          this.ctsUploadResultRecords = [];
        }

        setTimeout(() => {
          this.setCtsUploadTableData();
        }, 200);
      });
  }

  onCtsUploadYearChange(ob) {
    this.ctsUploadSelectedYear = ob.value;
    localStorage.setItem('ctsUploadSelectedYear', ob.value);
    this.onCtsUploadSearch();
    this.onCtsDashboardLazyLoadEvent(undefined);
  }

  onCtsUploadSearch() {
    this.ctsUploadInput.financialEndYear = this.ctsUploadSelectedYear;
    this.ctsUploadInput.exchangeReason = this.selectExchangeReason !== -1 ? this.selectExchangeReason : null;
    this.ctsUploadInput.ctsUploadStatus = this.selectCtsUploadStatus !== -1 ? this.selectCtsUploadStatus : null;
    this.ctsUploadInput.receivingCountry = this.selectReceivingCountry ? this.selectReceivingCountry : null;
    this.ctsUploadInput.skipCount = 0;
    this.ctsUploadInput.maxResultCount = this.ctsUploadPageSize;
    this.ctsPackageRequestService
      .getAllCtsPackageRequestByInput(this.ctsUploadInput)
      .subscribe((response) => {
        this.ctsUploadTotalRecords = response.totalCount;
        this.ctsUploadResultRecords = response.items;

        setTimeout(() => {
          this.setCtsUploadTableData();
        }, 200);
      });
  }

  onCtsUploadExchangeReasonChange(ob) {
    this.selectExchangeReason = Number(ob.value);
    // Keep the selected Exchange Reason in local storage.
    localStorage.setItem('selectExchangeReason', ob.value);
    this.onCtsUploadSearch();
  }

  onCtsUploadStatusChange(ob) {
    this.selectCtsUploadStatus = Number(ob.value);
    // Keep the selected Upload Status in local storage.
    localStorage.setItem('selectCtsUploadStatus', ob.value);
    this.onCtsUploadSearch();
  }

  onCtsUploadReceivingCountryChange(ob) {
    this.selectReceivingCountry = ob.value;
    // Keep the selected Receiving Country in local storage.
    localStorage.setItem('selectReceivingCountry', ob.value);
    this.onCtsUploadSearch();
  }

  onCtsUploadLinkClick(event: BdoTableCellLinkClickEvent) {
    if (event.columnId === 'viewExchangeRecords') {
      this.dialog.open(ViewAssociatedExchangeRecordsComponent, {
        width: '1200px',
        data: {
          row: event.rawData
        }
      });
    }
    else if (event.columnId === 'viewComments') {
      this.dialog.open(ViewCommentDialogComponent, {
        width: '1200px',
        data: {
          row: event.rawData
        }
      });
    }
    else if (event.columnId === 'dataPacket') {
      this.downloadCtsDataPacket(event.rawData);
    }
  }

  onCtsUploadActionClick(event: BdoTableRowActionClickEvent) {
    if (event.action === 'RegenerateDataPacket') {
      const dialogRef = this.dialog.open(RegeneratePacketDialogComponent, {
        width: '500px',
        data: {
          row: event?.data?.rawData
        }
      });

      dialogRef.afterClosed().subscribe((result) => {
        if (result && result.comment) {
          this.ctsPackageRequestService
            .regeneratePackage(event.data?.rawData?.ctsPackageId, result.comment).subscribe({
              next: (response) => {
                if (response.success) {
                  this.toasterService.success(response.message || 'Regenerate Package successfully requested', '', { life: 5000 });
                }
                else {
                  this.toasterService.warn(response.message || 'Regenerate Package couldn’t be requested. Please try again later', null, { life: 7000 });
                }
                this.onCtsUploadLazyLoadEvent(undefined);
                this.onCtsDashboardLazyLoadEvent(undefined);
              },
              error: (error) => {
                //this.toasterService.error(error?.error?.error?.message, null, { life: 5000 });
                console.error('Error requesting Regenerate Package:', error);
              }
            })
        }
      });
    }
    else if (event.action === 'ctsUpload') {
      this.sweetAlert.fireDialog({
        action: "submit",
        title: "CTS Upload",
        text: "Are you sure you would like to proceed?",
        type: "confirm"
      }, (confirm) => {
        if (confirm) {
          this.ctsPackageRequestService.uploadToCts(event.data?.rawData?.ctsPackageId).subscribe({
            next: (response) => {
              if (response.success) {
                this.toasterService.success(response.message || 'CTS Upload successfully completed', '', { life: 5000 });
              }
              else {
                this.toasterService.warn(response.message || 'CTS Upload couldn’t be completed. Please try again later', null, { life: 7000 });
              }
              this.onCtsUploadLazyLoadEvent(undefined);
              this.onCtsDashboardLazyLoadEvent(undefined);
            },
            error: (error) => {
              //this.toasterService.error(error?.error?.error?.message, null, { life: 5000 });
              console.error('Error requesting CTS Upload:', error);
            }
          });
        }
      });
    }
  }

  onCheckboxClick(event: BdoTableCheckboxClickEvent) {
    // Commented out: console.log('Checkbox clicked:', event.selectedRows[0]?.rawData?.ctsPackageId);
    // Commented out: console.log('Checkbox clicked:', event.rowId);
    if (event.isChecked) {
      this.ctsPackageRequestService.markAsDoNotUpload(event.rowId).subscribe({
        next: (response) => {
          if (response.success) {
            this.toasterService.success(response.message || 'Exclude packet from uploading successfully completed', '', { life: 5000 });
          }
          else {
            this.toasterService.warn(response.message || 'Exclude packet from uploading  couldn’t be completed. Please try again later', null, { life: 7000 });
          }
          this.onCtsUploadLazyLoadEvent(undefined);
          this.onCtsDashboardLazyLoadEvent(undefined);
        },
        error: (error) => {
          console.error('Error marking as Do Not Upload:', error);
        }
      });
    } else {
      this.ctsPackageRequestService.unMarkAsDoNotUpload(event.rowId).subscribe({
        next: (response) => {
          if (response.success) {
            this.toasterService.success(response.message || 'Include packet for uploading successfully completed', '', { life: 5000 });
          }
          else {
            this.toasterService.warn(response.message || 'Include packet for uploading couldn’t be completed. Please try again later', null, { life: 7000 });
          }
          this.onCtsUploadLazyLoadEvent(undefined);
          this.onCtsDashboardLazyLoadEvent(undefined);
        },
        error: (error) => {
          console.error('Error unmarking as Do Not Upload:', error);
        }
      });
    }
  }

  // Add this method to open the Upload Historical XML dialog
  openUploadHistoricalXmlDialog() {
    const dialogRef = this.dialog.open(UploadHistoricalXmlDialogComponent, {
      width: '500px',
      data: {
        receivingCountries: this.uploadHistoricalCountries || [],
        fiscalYears: this.year || []
      }
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result && result.success) {
        this.onCtsUploadLazyLoadEvent(undefined);
        this.onCtsDashboardLazyLoadEvent(undefined);
      }
    });
  }

  openCtsUploadDialog() {
    this.sweetAlert.fireDialog({
      action: "submit",
      title: "CTS Upload",
      text: `Are you sure you want to upload data packets with a Financial Period End in ${this.ctsUploadSelectedYear}? Once confirmed, all data packets with a status of Not Started for the year will be uploaded to the CTS platform.`,
      type: "confirm"
    }, (confirm) => {
      if (confirm) {
        this.ctsPackageRequestService
          .batchUploadToCts(Number(this.ctsUploadSelectedYear)).subscribe({
            next: (response) => {
              if (response.success) {
                this.toasterService.success(response.message || 'CTS Upload successfully requested', '', { life: 5000 });
              }
              else {
                this.toasterService.warn(response.message || 'CTS Upload couldn’t be requested. Please try again later', null, { life: 7000 });
              }
              this.onCtsUploadLazyLoadEvent(undefined);
              this.onCtsDashboardLazyLoadEvent(undefined);
            },
            error: (error) => {
              //this.toasterService.error(error?.error?.error?.message, null, { life: 5000 });
              console.error('Error requesting CTS Upload:', error);
            }
          })
      }
    });

    // const dialogRef = this.dialog.open(UploadCtsDialogComponent, {
    //   width: '500px',
    //   data: {
    //     fiscalYears: this.year || []
    //   }
    // });

    // dialogRef.afterClosed().subscribe((result) => {
    //   if (result && result.fiscalYear) {
    //     this.ctsPackageRequestService
    //       .batchUploadToCts(result.fiscalYear).subscribe({
    //         next: (response) => {
    //           if (response.success) {
    //             this.toasterService.success(response.message || 'CTS Upload successfully requested', '', { life: 5000 });
    //           }
    //           else {
    //             this.toasterService.warn(response.message || 'CTS Upload couldn’t be requested. Please try again later', null, { life: 7000 });
    //           }
    //           this.onCtsUploadLazyLoadEvent(undefined);
    //           this.onCtsDashboardLazyLoadEvent(undefined);
    //         },
    //         error: (error) => {
    //           //this.toasterService.error(error?.error?.error?.message, null, { life: 5000 });
    //           console.error('Error requesting CTS Upload:', error);
    //         }
    //       })
    //   }
    // });
  }

  openDecryptDialog() {
    const dialogRef = this.dialog.open(DecryptDataPacketDialogComponent, {
      width: '500px'
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result && result.file) {
        const formData: FormData = new FormData();
        formData.append('fileName', result.file.name);
        formData.append('file', result.file);
        formData.append('fileType', result.file.type);
        // Only call upload if file is present        
        this.fileUploadService
          .unpackCtsPackage(formData).subscribe({
            next: (response) => {
              if (response.success) {
                this.toasterService.success(response.message || 'Decrypt Data Packet successfully completed', '', { life: 5000 });
              }
              else {
                this.toasterService.warn(response.message || 'Decrypt Data Packet couldn’t be completed. Please try again later', null, { life: 7000 });
              }
              this.onCtsUploadLazyLoadEvent(undefined);
              this.onCtsDashboardLazyLoadEvent(undefined);
            },
            error: (error) => {
              //this.toasterService.error(error?.error?.error?.message, null, { life: 5000 });
              console.error('Error decrypting data packet:', error);
            }
          })
      }
    });
  }

  refreshAllTransmissionStatus() {
    this.sweetAlert.fireDialog({
      action: "submit",
      title: "Refresh All Transmission Status",
      text: "Are you sure you would like to proceed?",
      type: "confirm"
    }, (confirm) => {
      if (confirm) {
        this.ctsPackageRequestService.refreshTransmissionStatus().subscribe({
          next: (response) => {
            if (response.success) {
              this.toasterService.success(response.message || 'Refresh All Transmission Status successfully requested', '', { life: 5000 });
            }
            else {
              this.toasterService.warn(response.message || 'Refresh All Transmission Status couldn’t be requested. Please try again later', null, { life: 7000 });
            }
            this.onCtsUploadLazyLoadEvent(undefined);
            this.onCtsDashboardLazyLoadEvent(undefined);
          },
          error: (error) => {
            //this.toasterService.error(error?.error?.error?.message, null, { life: 5000 });
            console.error('Error requesting Refresh All Transmission Status:', error);
          }
        });
      }
    });
  }

  downloadCtsDataPacket(row: any) {
    this.sweetAlert.fireDialog({
      action: "submit",
      title: "Download CTS Data Packet",
      text: "Are you sure you would like to download?",
      type: "confirm"
    }, (confirm) => {
      if (confirm) {
        this.ctsPackageRequestService.downloadDataPacketFile(row?.ctsPackageId).subscribe({
          next: (response) => {
            if (response) {
              this.downloadFile(response, row?.dataPacket);
              this.toasterService.success('Download CTS Data Packet successfully completed', '', { life: 5000 });
            }
            else {
              this.toasterService.warn('Download CTS Data Packet couldn’t be completed. Please try again later', null, { life: 7000 });
            }
          },
          error: (error) => {
            // this.toasterService.error('Error Download CTS Data Packet', null, { life: 200000 });            
            console.error('Error Download CTS Data Packet:', error);
          }
        });
      }
    });
  }
}
