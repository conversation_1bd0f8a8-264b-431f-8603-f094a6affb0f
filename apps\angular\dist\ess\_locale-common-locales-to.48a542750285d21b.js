"use strict";(self.webpackChunkess=self.webpackChunkess||[]).push([[2062],{56149:(l,u,e)=>{e.r(u),e.d(u,{default:()=>s});const a=void 0,s=["to",[["<PERSON>","PM"],a,["heng<PERSON><PERSON><PERSON>","e<PERSON><PERSON>"]],[["AM","PM"],a,["HH","EA"]],[["S","M","T","P","T","F","T"],["S\u0101p","M\u014dn","T\u016bs","Pul","Tu\u02bba","Fal","Tok"],["S\u0101pate","M\u014dnite","T\u016bsite","Pulelulu","Tu\u02bbapulelulu","Falaite","Tokonaki"],["S\u0101p","M\u014dn","T\u016bs","<PERSON><PERSON>","Tu\u02bba","Fal","Tok"]],a,[["S","F","M","E","M","S","S","A","S","O","N","T"],["S\u0101n","F\u0113p","Ma\u02bba","\u02bbEpe","M\u0113","Sun","Siu","\u02bbAok","Sep","\u02bbOka","N\u014dv","T\u012bs"],["S\u0101nuali","F\u0113pueli","Ma\u02bbasi","\u02bbEpeleli","M\u0113","Sune","Siulai","\u02bbAokosi","Sepitema","\u02bbOkatopa","N\u014dvema","T\u012bsema"]],a,[["KM","TS"],a,["ki mu\u02bba","ta\u02bbu \u02bbo S\u012bs\u016b"]],1,[6,0],["d/M/yy","d MMM y","d MMMM y","EEEE d MMMM y"],["h:mm a","h:mm:ss a","h:mm:ss a z","h:mm:ss a zzzz"],["{1} {0}","{1}, {0}",a,a],[".",",",";","%","+","-","E","\xd7","\u2030","\u221e","TF",":"],["#,##0.###","#,##0%","\xa4\xa0#,##0.00","#E0"],"TOP","T$","Pa\u02bbanga fakatonga",{AUD:["AUD$","AU$"],FJD:[a,"F$"],JPY:["JP\xa5","\xa5"],NZD:["NZD$","NZ$"],SBD:[a,"S$"],TOP:["T$"],USD:["US$","$"]},"ltr",function M(n){return 5}]}}]);