"use strict";(self.webpackChunkess=self.webpackChunkess||[]).push([[43247],{92636:(A,a,s)=>{s.r(a),s.d(a,{default:()=>t});const e=void 0,t=["wo",[["Sub","Ngo"],e,e],e,[["Dib","Alt","Tal","\xc0la","Alx","\xc0jj","Ase"],e,["Dib\xe9er","<PERSON><PERSON>","Tala<PERSON>","\xc0lar<PERSON>","<PERSON><PERSON><PERSON>","\xc0jjuma","<PERSON>eer"],["Dib","Alt","Tal","\xc0la","Alx","\xc0jj","Ase"]],e,[["1","2","3","4","5","6","7","8","9","10","11","12"],["<PERSON>","<PERSON>","<PERSON>","<PERSON>wr","<PERSON><PERSON>","<PERSON>w","<PERSON>","Ut","S\xe0t","Okt","Now","<PERSON>"],["Samwiyee","<PERSON><PERSON>yee","<PERSON>","Awril","Mee","Suwe","Sulet","Ut","S\xe0ttumbar","Oktoobar","Now\xe0mbar","Des\xe0mbar"]],e,[["JC","AD"],e,["av. JC","AD"]],1,[6,0],["dd-MM-y","d MMM, y","d MMMM, y","EEEE, d MMM, y"],["HH:mm","HH:mm:ss","HH:mm:ss z","HH:mm:ss zzzz"],["{1} - {0}",e,"{1} 'ci' {0}",e],[",",".",";","%","+","-","E","\xd7","\u2030","\u221e","NaN",":"],["#,##0.###","#,##0%","\xa4\xa0#,##0.00","#E0"],"XOF","F\u202fCFA","Franc CFA bu Afrik Sowwu-jant",{JPY:["JP\xa5","\xa5"]},"ltr",function l(u){return 5}]}}]);