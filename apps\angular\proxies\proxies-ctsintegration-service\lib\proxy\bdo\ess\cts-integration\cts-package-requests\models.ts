import type { DataPacketAction } from '../constants/data-packet-action.enum';
import type { EntityDto } from '@abp/ng.core';
import type { ExchangeReason } from '../../shared/constants/information-exchanges/exchange-reason.enum';
import type { CTSUploadStatus } from '../enums/ctsupload-status.enum';

export interface CtsActionResultDto {
  errors: string[];
  success: boolean;
  message?: string;
  availableActions: DataPacketAction[];
}

export interface CtsPackageRequestDataDto extends EntityDto<string> {
  essInformationXmlId?: string;
  ctsPackageFileName?: string;
  receiverCountryCode?: string;
  fiscalYear: number;
  exchangeReason?: ExchangeReason;
  xmlPayload?: string;
  xmlPayloadUrl?: string;
  packageZipUrl?: string;
  fileCreatedAt?: string;
  uploadedAt?: string;
  uploadStatus?: CTSUploadStatus;
  transmissionStatus?: string;
  isExcludeCtsUpload: boolean;
  statusUpdatedAt?: string;
  hasExchangeRecords: boolean;
  tenantId?: string;
  messageRefId?: string;
}
