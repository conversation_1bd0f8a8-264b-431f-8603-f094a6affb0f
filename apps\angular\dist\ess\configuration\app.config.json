{"timeOutSecond": 1800, "version": {"number": "1.0", "build": "LOCAL", "buildId": ""}, "appInsights": {"enabled": true, "enableLogPageViews": true, "instrumentationKey": "91cf4c74-09a1-441c-93c1-aa63563f1676"}, "app": {"name": "ESS", "localDev": false, "enableMockData": false, "isProd": true, "supportEmail": "<EMAIL>", "supportPhone": "+****************"}, "application": {"baseUrl": "https://{0}.ess-portal.ess.bs/", "name": "Ess"}, "apis": {"default": {"url": "https://{0}.ess-portal-gateway.ess.bs/", "rootNamespace": "Bdo.Ess"}, "AbpAccountPublic": {"url": "https://{0}.ess-portal-authserver.ess.bs/", "rootNamespace": "AbpAccountPublic"}, "ProductService": {"url": "https://{0}.ess-portal-gateway.ess.bs/", "rootNamespace": "Bdo.Ess.ProductService"}}, "oAuthConfig": {"issuer": "https://{0}.ess-portal-authserver.ess.bs/", "clientId": "Angular", "skipIssuerCheck": true, "responseType": "code", "scope": "offline_access openid profile email phone AccountService IdentityService AdministrationService SaasService EconomicSubstanceService CorporateEntityService SearchService LookupService AuditService DashboardService CtsIntegrationService", "requireHttps": true, "redirectUri": "https://{0}.ess-portal.ess.bs/"}}