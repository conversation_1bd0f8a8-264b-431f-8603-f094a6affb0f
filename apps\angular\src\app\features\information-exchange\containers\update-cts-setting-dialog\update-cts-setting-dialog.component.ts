import { ToasterService } from '@abp/ng.theme.shared';
import { Component, Inject } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { SweetAlertService } from '@app/shared/services/sweetalert.service';
import { BahamasCtsSettingDto } from 'proxies/proxies-ctsintegration-service/lib/proxy/bdo/ess/cts-integration/bahamas-cts-settings';

@Component({
  selector: 'app-update-cts-setting-dialog',
  templateUrl: './update-cts-setting-dialog.component.html',
  styleUrls: ['./update-cts-setting-dialog.component.scss']
})
export class UpdateCtsSettingDialogComponent {
  form: FormGroup;
  selectedFile: File | null = null;
  selectedCertificateFile: File | null = null;
  passwordVisible = false;

  get displaySftpSshKey(): string {
    const key = this.data?.sftpSSHKey;
    if (key && key.length > 30) {
      return `${key.substring(0, 30)}...`;
    }
    return key || '';
  }

  get displayCtsPublicCertificate(): string {
    const cert = this.data?.ctsPublicCertificate;
    if (cert && cert.length > 30) {
      return `${cert.substring(0, 30)}...`;
    }
    return cert || '';
  }

  constructor(
    public dialogRef: MatDialogRef<UpdateCtsSettingDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: BahamasCtsSettingDto,
    private sweetAlert: SweetAlertService,
    private toasterService: ToasterService,
    private fb: FormBuilder
  ) {
    this.form = this.fb.group({
      systemUserName: [data?.systemUserName || '', Validators.required],
      systemUserPassword: [data?.systemUserPassword, Validators.required],
      sftpUserName: [data?.sftpUserName || '', Validators.required],
      sftpSshKey: [{ value: this.displaySftpSshKey || '', disabled: true }],
      ctsPublicCertificate: [{ value: this.displayCtsPublicCertificate || '', disabled: true }],
      file: [null],
      certificateFile: [null]
    });
  }

  onFileChange(event: any) {
    const file = event.target.files[0];
    if (file) {
      this.selectedFile = file;
      this.form.patchValue({ file });
      this.form.get('file')?.updateValueAndValidity();
    } else {
      this.selectedFile = null;
      this.form.patchValue({ file: null });
      this.form.get('file')?.updateValueAndValidity();
    }
  }

  onCertificateFileChange(event: any) {
    const file = event.target.files[0];
    if (file) {
      if (!file.name.endsWith('.crt') && !file.name.endsWith('.cer') && !file.name.endsWith('.txt')) {
        this.selectedCertificateFile = null;
        this.form.patchValue({ certificateFile: null });
        this.form.get('certificateFile')?.updateValueAndValidity();
        this.toasterService.warn('Only .crt, .cer, and .txt files are allowed.', null, { life: 7000 });
        return;
      }
      this.selectedCertificateFile = file;
      this.form.patchValue({ certificateFile: file });
      this.form.get('certificateFile')?.updateValueAndValidity();
    } else {
      this.selectedCertificateFile = null;
      this.form.patchValue({ certificateFile: null });
      this.form.get('certificateFile')?.updateValueAndValidity();
    }
  }

  togglePasswordVisibility() {
    this.passwordVisible = !this.passwordVisible;
  }

  onCancel(): void {
    if (this.form.dirty) {
      this.sweetAlert.fireDialog({
        action: "delete", title: "Are you sure you want to close?",
        text: "Any unsaved changes may be lost", type: "confirm"
      }, (confirm) => {
        if (confirm) {
          this.dialogRef.close();
        }
      });
    } else {
      this.dialogRef.close();
    }
  }

  onSubmit(): void {
    if (this.form.invalid) return;
    this.dialogRef.close({
      ...this.form.value,
      id: this.data?.id,
      file: this.selectedFile,
      certificateFile: this.selectedCertificateFile
    });
  }
}
