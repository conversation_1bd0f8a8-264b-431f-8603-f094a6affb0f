"use strict";(self.webpackChunkess=self.webpackChunkess||[]).push([[34821],{44738:(H,n,t)=>{t.r(n),t.d(n,{default:()=>d});const s=void 0,d=["mk",[["\u043f\u0440\u0435\u0442\u043f\u043b.","\u043f\u043e\u043f\u043b."],s,["\u043f\u0440\u0435\u0442\u043f\u043b\u0430\u0434\u043d\u0435","\u043f\u043e\u043f\u043b\u0430\u0434\u043d\u0435"]],s,[["\u043d","\u043f","\u0432","\u0441","\u0447","\u043f","\u0441"],["\u043d\u0435\u0434.","\u043f\u043e\u043d.","\u0432\u0442\u043e.","\u0441\u0440\u0435.","\u0447\u0435\u0442.","\u043f\u0435\u0442.","\u0441\u0430\u0431."],["\u043d\u0435\u0434\u0435\u043b\u0430","\u043f\u043e\u043d\u0435\u0434\u0435\u043b\u043d\u0438\u043a","\u0432\u0442\u043e\u0440\u043d\u0438\u043a","\u0441\u0440\u0435\u0434\u0430","\u0447\u0435\u0442\u0432\u0440\u0442\u043e\u043a","\u043f\u0435\u0442\u043e\u043a","\u0441\u0430\u0431\u043e\u0442\u0430"],["\u043d\u0435\u0434.","\u043f\u043e\u043d.","\u0432\u0442\u043e.","\u0441\u0440\u0435.","\u0447\u0435\u0442.","\u043f\u0435\u0442.","\u0441\u0430\u0431."]],s,[["\u0458","\u0444","\u043c","\u0430","\u043c","\u0458","\u0458","\u0430","\u0441","\u043e","\u043d","\u0434"],["\u0458\u0430\u043d.","\u0444\u0435\u0432.","\u043c\u0430\u0440.","\u0430\u043f\u0440.","\u043c\u0430\u0458","\u0458\u0443\u043d.","\u0458\u0443\u043b.","\u0430\u0432\u0433.","\u0441\u0435\u043f\u0442.","\u043e\u043a\u0442.","\u043d\u043e\u0435\u043c.","\u0434\u0435\u043a."],["\u0458\u0430\u043d\u0443\u0430\u0440\u0438","\u0444\u0435\u0432\u0440\u0443\u0430\u0440\u0438","\u043c\u0430\u0440\u0442","\u0430\u043f\u0440\u0438\u043b","\u043c\u0430\u0458","\u0458\u0443\u043d\u0438","\u0458\u0443\u043b\u0438","\u0430\u0432\u0433\u0443\u0441\u0442","\u0441\u0435\u043f\u0442\u0435\u043c\u0432\u0440\u0438","\u043e\u043a\u0442\u043e\u043c\u0432\u0440\u0438","\u043d\u043e\u0435\u043c\u0432\u0440\u0438","\u0434\u0435\u043a\u0435\u043c\u0432\u0440\u0438"]],s,[["\u043f.\u043d.\u0435.","\u043d.\u0435."],s,["\u043f\u0440\u0435\u0434 \u043d\u0430\u0448\u0430\u0442\u0430 \u0435\u0440\u0430","\u043e\u0434 \u043d\u0430\u0448\u0430\u0442\u0430 \u0435\u0440\u0430"]],1,[6,0],["d.M.yy","d.M.y","d MMMM y","EEEE, d MMMM y"],["HH:mm","HH:mm:ss","HH:mm:ss z","HH:mm:ss zzzz"],["{1}, '\u0432\u043e' {0}",s,s,s],[",",".",";","%","+","-","E","\xd7","\u2030","\u221e","NaN",":"],["#,##0.###","#,##0\xa0%","#,##0.00\xa0\xa4","#E0"],"MKD","\u0434\u0435\u043d.","\u041c\u0430\u043a\u0435\u0434\u043e\u043d\u0441\u043a\u0438 \u0434\u0435\u043d\u0430\u0440",{AUD:[s,"$"],BYN:[s,"\u0440."],CNY:[s,"\xa5"],GBP:[s,"\xa3"],HKD:[s,"$"],ILS:[s,"\u20aa"],INR:[s,"\u20b9"],JPY:[s,"\xa5"],KRW:[s,"\u20a9"],MKD:["\u0434\u0435\u043d."],NZD:[s,"$"],PHP:[s,"\u20b1"],TWD:[s,"NT$"],USD:["US$","$"],VND:[s,"\u20ab"]},"ltr",function r(e){const M=Math.floor(Math.abs(e)),m=e.toString().replace(/^[^.]*\.?/,"").length,u=parseInt(e.toString().replace(/^[^.]*\.?/,""),10)||0;return 0===m&&M%10==1&&M%100!=11||u%10==1&&u%100!=11?1:5}]}}]);