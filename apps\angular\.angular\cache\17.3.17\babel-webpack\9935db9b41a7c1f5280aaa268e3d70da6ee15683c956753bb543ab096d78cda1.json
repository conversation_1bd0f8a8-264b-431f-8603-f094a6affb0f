{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport { MAT_DIALOG_DATA } from '@angular/material/dialog';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/dialog\";\nimport * as i2 from \"@app/shared/services/sweetalert.service\";\nimport * as i3 from \"@abp/ng.theme.shared\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"@angular/material/input\";\nimport * as i6 from \"@angular/material/form-field\";\nimport * as i7 from \"@angular/material/icon\";\nimport * as i8 from \"@angular/material/button\";\nimport * as i9 from \"@angular/material/tooltip\";\nimport * as i10 from \"@ngx-validate/core\";\nimport * as i11 from \"@angular/common\";\nfunction UpdateCtsSettingDialogComponent_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" System User Password last updated at: \", i0.ɵɵpipeBind2(2, 1, ctx_r1.data == null ? null : ctx_r1.data.systemUserPasswordUpdatedAt, \"dd/MM/yyyy\"), \" \");\n  }\n}\nfunction UpdateCtsSettingDialogComponent_span_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 31);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"matTooltip\", ctx_r1.selectedFile.name);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.selectedFile.name, \" \");\n  }\n}\nfunction UpdateCtsSettingDialogComponent_ng_template_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 32);\n    i0.ɵɵtext(1, \"No file chosen\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UpdateCtsSettingDialogComponent_div_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" SFTP SSH Key last updated at: \", i0.ɵɵpipeBind2(2, 1, ctx_r1.data == null ? null : ctx_r1.data.sftpSSHKeyUpdatedAt, \"dd/MM/yyyy\"), \" \");\n  }\n}\nfunction UpdateCtsSettingDialogComponent_span_67_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 31);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"matTooltip\", ctx_r1.selectedCertificateFile.name);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.selectedCertificateFile.name, \" \");\n  }\n}\nfunction UpdateCtsSettingDialogComponent_ng_template_68_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 32);\n    i0.ɵɵtext(1, \"No file chosen\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UpdateCtsSettingDialogComponent_div_73_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" CTS Public Certificate last updated at: \", i0.ɵɵpipeBind2(2, 1, ctx_r1.data == null ? null : ctx_r1.data.ctsPublicCertificateUpdatedAt, \"dd/MM/yyyy\"), \" \");\n  }\n}\nexport class UpdateCtsSettingDialogComponent {\n  get displaySftpSshKey() {\n    const key = this.data?.sftpSSHKey;\n    if (key && key.length > 30) {\n      return `${key.substring(0, 30)}...`;\n    }\n    return key || '';\n  }\n  get displayCtsPublicCertificate() {\n    const cert = this.data?.ctsPublicCertificate;\n    if (cert && cert.length > 30) {\n      return `${cert.substring(0, 30)}...`;\n    }\n    return cert || '';\n  }\n  constructor(dialogRef, data, sweetAlert, toasterService, fb) {\n    this.dialogRef = dialogRef;\n    this.data = data;\n    this.sweetAlert = sweetAlert;\n    this.toasterService = toasterService;\n    this.fb = fb;\n    this.selectedFile = null;\n    this.selectedCertificateFile = null;\n    this.passwordVisible = false;\n    this.form = this.fb.group({\n      systemUserName: [data?.systemUserName || '', Validators.required],\n      systemUserPassword: [data?.systemUserPassword, Validators.required],\n      sftpUserName: [data?.sftpUserName || '', Validators.required],\n      sftpSshKey: [{\n        value: this.displaySftpSshKey || '',\n        disabled: true\n      }],\n      ctsPublicCertificate: [{\n        value: this.displayCtsPublicCertificate || '',\n        disabled: true\n      }],\n      file: [null],\n      certificateFile: [null]\n    });\n  }\n  onFileChange(event) {\n    const file = event.target.files[0];\n    if (file) {\n      this.selectedFile = file;\n      this.form.patchValue({\n        file\n      });\n      this.form.get('file')?.updateValueAndValidity();\n    } else {\n      this.selectedFile = null;\n      this.form.patchValue({\n        file: null\n      });\n      this.form.get('file')?.updateValueAndValidity();\n    }\n  }\n  onCertificateFileChange(event) {\n    const file = event.target.files[0];\n    if (file) {\n      if (!file.name.endsWith('.crt') && !file.name.endsWith('.cer') && !file.name.endsWith('.txt')) {\n        this.selectedCertificateFile = null;\n        this.form.patchValue({\n          certificateFile: null\n        });\n        this.form.get('certificateFile')?.updateValueAndValidity();\n        this.toasterService.warn('Only .crt, .cer, and .txt files are allowed.', null, {\n          life: 7000\n        });\n        return;\n      }\n      this.selectedCertificateFile = file;\n      this.form.patchValue({\n        certificateFile: file\n      });\n      this.form.get('certificateFile')?.updateValueAndValidity();\n    } else {\n      this.selectedCertificateFile = null;\n      this.form.patchValue({\n        certificateFile: null\n      });\n      this.form.get('certificateFile')?.updateValueAndValidity();\n    }\n  }\n  togglePasswordVisibility() {\n    this.passwordVisible = !this.passwordVisible;\n  }\n  onCancel() {\n    if (this.form.dirty) {\n      this.sweetAlert.fireDialog({\n        action: \"delete\",\n        title: \"Are you sure you want to close?\",\n        text: \"Any unsaved changes may be lost\",\n        type: \"confirm\"\n      }, confirm => {\n        if (confirm) {\n          this.dialogRef.close();\n        }\n      });\n    } else {\n      this.dialogRef.close();\n    }\n  }\n  onSubmit() {\n    if (this.form.invalid) return;\n    this.dialogRef.close({\n      ...this.form.value,\n      id: this.data?.id,\n      file: this.selectedFile,\n      certificateFile: this.selectedCertificateFile\n    });\n  }\n  static {\n    this.ɵfac = function UpdateCtsSettingDialogComponent_Factory(t) {\n      return new (t || UpdateCtsSettingDialogComponent)(i0.ɵɵdirectiveInject(i1.MatDialogRef), i0.ɵɵdirectiveInject(MAT_DIALOG_DATA), i0.ɵɵdirectiveInject(i2.SweetAlertService), i0.ɵɵdirectiveInject(i3.ToasterService), i0.ɵɵdirectiveInject(i4.FormBuilder));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: UpdateCtsSettingDialogComponent,\n      selectors: [[\"app-update-cts-setting-dialog\"]],\n      decls: 79,\n      vars: 11,\n      consts: [[\"noFile\", \"\"], [\"fileInput\", \"\"], [\"noCertificateFile\", \"\"], [\"certificateFileInput\", \"\"], [\"mat-dialog-title\", \"\"], [1, \"row\"], [1, \"col-8\", \"title\"], [1, \"col-4\", \"text-end\", \"modal-action-button\"], [\"type\", \"button\", \"mat-raised-button\", \"\", 1, \"ui-button\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [3, \"ngSubmit\", \"formGroup\"], [1, \"col-md-4\"], [1, \"w-100\"], [\"matInput\", \"\", \"formControlName\", \"systemUserName\", \"required\", \"\"], [\"matInput\", \"\", \"formControlName\", \"systemUserPassword\", \"required\", \"\", 3, \"type\"], [\"mat-icon-button\", \"\", \"matSuffix\", \"\", \"type\", \"button\", 3, \"click\"], [\"class\", \"last-updated\", 4, \"ngIf\"], [\"matInput\", \"\", \"formControlName\", \"sftpUserName\", \"required\", \"\"], [\"matInput\", \"\", \"formControlName\", \"sftpSshKey\"], [1, \"file-upload-group\"], [1, \"file-upload-label\"], [1, \"file-upload-row\"], [\"mat-stroked-button\", \"\", \"color\", \"primary\", \"type\", \"button\", 3, \"click\"], [\"class\", \"file-name\", 3, \"matTooltip\", 4, \"ngIf\", \"ngIfElse\"], [\"accept\", \"*\", \"type\", \"file\", \"formControlName\", \"file\", 1, \"file-input\", 3, \"change\"], [\"matInput\", \"\", \"formControlName\", \"ctsPublicCertificate\"], [\"accept\", \".crt,.cer,.txt\", \"type\", \"file\", \"formControlName\", \"certificateFile\", 1, \"file-input\", 3, \"change\"], [\"align\", \"end\"], [\"mat-raised-button\", \"\", \"type\", \"button\", 3, \"click\"], [\"mat-raised-button\", \"\", \"type\", \"submit\", 1, \"ui-button\", 3, \"disabled\"], [1, \"last-updated\"], [1, \"file-name\", 3, \"matTooltip\"], [1, \"file-placeholder\"]],\n      template: function UpdateCtsSettingDialogComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 4)(1, \"div\", 5)(2, \"div\", 6);\n          i0.ɵɵtext(3, \"CTS Settings\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 7)(5, \"button\", 8);\n          i0.ɵɵlistener(\"click\", function UpdateCtsSettingDialogComponent_Template_button_click_5_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onCancel());\n          });\n          i0.ɵɵelement(6, \"i\", 9);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(7, \"form\", 10);\n          i0.ɵɵlistener(\"ngSubmit\", function UpdateCtsSettingDialogComponent_Template_form_ngSubmit_7_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSubmit());\n          });\n          i0.ɵɵelementStart(8, \"mat-dialog-content\")(9, \"div\", 5)(10, \"div\", 11)(11, \"mat-form-field\", 12)(12, \"mat-label\");\n          i0.ɵɵtext(13, \"System User Name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(14, \"input\", 13);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(15, \"div\", 11)(16, \"mat-form-field\", 12)(17, \"mat-label\");\n          i0.ɵɵtext(18, \"System User Password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(19, \"input\", 14);\n          i0.ɵɵelementStart(20, \"button\", 15);\n          i0.ɵɵlistener(\"click\", function UpdateCtsSettingDialogComponent_Template_button_click_20_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.togglePasswordVisibility());\n          });\n          i0.ɵɵelementStart(21, \"mat-icon\");\n          i0.ɵɵtext(22);\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(23, \"div\", 5);\n          i0.ɵɵtemplate(24, UpdateCtsSettingDialogComponent_div_24_Template, 3, 4, \"div\", 16);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"div\", 5)(26, \"div\", 11)(27, \"mat-form-field\", 12)(28, \"mat-label\");\n          i0.ɵɵtext(29, \"SFTP User Name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(30, \"input\", 17);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(31, \"div\", 11)(32, \"mat-form-field\", 12)(33, \"mat-label\");\n          i0.ɵɵtext(34, \"SFTP SSH Key\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(35, \"input\", 18);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(36, \"div\", 11)(37, \"div\", 19)(38, \"label\", 20);\n          i0.ɵɵtext(39, \"Upload New SSH Key File\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(40, \"div\", 21)(41, \"button\", 22);\n          i0.ɵɵlistener(\"click\", function UpdateCtsSettingDialogComponent_Template_button_click_41_listener() {\n            i0.ɵɵrestoreView(_r1);\n            const fileInput_r3 = i0.ɵɵreference(49);\n            return i0.ɵɵresetView(fileInput_r3.click());\n          });\n          i0.ɵɵelementStart(42, \"mat-icon\");\n          i0.ɵɵtext(43, \"upload_file\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(44, \" Choose File \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(45, UpdateCtsSettingDialogComponent_span_45_Template, 2, 2, \"span\", 23)(46, UpdateCtsSettingDialogComponent_ng_template_46_Template, 2, 0, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(48, \"input\", 24, 1);\n          i0.ɵɵlistener(\"change\", function UpdateCtsSettingDialogComponent_Template_input_change_48_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onFileChange($event));\n          });\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(50, \"div\", 5);\n          i0.ɵɵtemplate(51, UpdateCtsSettingDialogComponent_div_51_Template, 3, 4, \"div\", 16);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(52, \"div\", 5)(53, \"div\", 11)(54, \"mat-form-field\", 12)(55, \"mat-label\");\n          i0.ɵɵtext(56, \"CTS Public Certificate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(57, \"input\", 25);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(58, \"div\", 11)(59, \"div\", 19)(60, \"label\", 20);\n          i0.ɵɵtext(61, \"Upload New CTS Public Certificate File\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(62, \"div\", 21)(63, \"button\", 22);\n          i0.ɵɵlistener(\"click\", function UpdateCtsSettingDialogComponent_Template_button_click_63_listener() {\n            i0.ɵɵrestoreView(_r1);\n            const certificateFileInput_r4 = i0.ɵɵreference(71);\n            return i0.ɵɵresetView(certificateFileInput_r4.click());\n          });\n          i0.ɵɵelementStart(64, \"mat-icon\");\n          i0.ɵɵtext(65, \"upload_file\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(66, \" Choose File \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(67, UpdateCtsSettingDialogComponent_span_67_Template, 2, 2, \"span\", 23)(68, UpdateCtsSettingDialogComponent_ng_template_68_Template, 2, 0, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(70, \"input\", 26, 3);\n          i0.ɵɵlistener(\"change\", function UpdateCtsSettingDialogComponent_Template_input_change_70_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onCertificateFileChange($event));\n          });\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(72, \"div\", 5);\n          i0.ɵɵtemplate(73, UpdateCtsSettingDialogComponent_div_73_Template, 3, 4, \"div\", 16);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(74, \"mat-dialog-actions\", 27)(75, \"button\", 28);\n          i0.ɵɵlistener(\"click\", function UpdateCtsSettingDialogComponent_Template_button_click_75_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onCancel());\n          });\n          i0.ɵɵtext(76, \"Cancel\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(77, \"button\", 29);\n          i0.ɵɵtext(78, \"Save\");\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          const noFile_r5 = i0.ɵɵreference(47);\n          const noCertificateFile_r6 = i0.ɵɵreference(69);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"formGroup\", ctx.form);\n          i0.ɵɵadvance(12);\n          i0.ɵɵproperty(\"type\", ctx.passwordVisible ? \"text\" : \"password\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.passwordVisible ? \"visibility_off\" : \"visibility\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.data == null ? null : ctx.data.systemUserPasswordUpdatedAt);\n          i0.ɵɵadvance(21);\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedFile)(\"ngIfElse\", noFile_r5);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", ctx.data == null ? null : ctx.data.sftpSSHKeyUpdatedAt);\n          i0.ɵɵadvance(16);\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedCertificateFile)(\"ngIfElse\", noCertificateFile_r6);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", ctx.data == null ? null : ctx.data.ctsPublicCertificateUpdatedAt);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"disabled\", ctx.form.invalid);\n        }\n      },\n      dependencies: [i4.ɵNgNoValidate, i4.DefaultValueAccessor, i4.NgControlStatus, i4.NgControlStatusGroup, i4.RequiredValidator, i4.FormGroupDirective, i4.FormControlName, i5.MatInput, i6.MatFormField, i6.MatLabel, i6.MatSuffix, i7.MatIcon, i1.MatDialogTitle, i1.MatDialogActions, i1.MatDialogContent, i8.MatButton, i8.MatIconButton, i9.MatTooltip, i10.ValidationGroupDirective, i10.ValidationDirective, i11.NgIf, i11.DatePipe],\n      styles: [\".title[_ngcontent-%COMP%] {\\n  font-size: 1.3em;\\n  color: #00779b;\\n  display: block;\\n}\\n\\n.modal-action-button[_ngcontent-%COMP%] {\\n  font-size: 1em;\\n}\\n\\n.action-buttons[_ngcontent-%COMP%] {\\n  margin-top: 16px;\\n  display: flex;\\n}\\n\\n.w-100[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n.last-updated[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #666;\\n  margin-top: -10px;\\n  margin-bottom: 10px;\\n}\\n\\n.file-name-row[_ngcontent-%COMP%] {\\n  min-height: 24px;\\n  display: flex;\\n  align-items: center;\\n  margin-top: 2px;\\n}\\n\\n.aligned-message[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-left: 8px;\\n  margin-bottom: 8px;\\n}\\n\\n.file-upload-group[_ngcontent-%COMP%] {\\n  margin-bottom: 16px;\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.file-upload-label[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  margin-bottom: 4px;\\n}\\n\\n.file-upload-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  margin-bottom: 4px;\\n}\\n\\n.file-input[_ngcontent-%COMP%] {\\n  display: none;\\n}\\n\\n.file-name[_ngcontent-%COMP%] {\\n  flex: 1 1 auto;\\n  max-width: 200px;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n  color: #333;\\n  cursor: pointer;\\n}\\n\\n.file-placeholder[_ngcontent-%COMP%] {\\n  color: #888;\\n  font-style: italic;\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvZmVhdHVyZXMvaW5mb3JtYXRpb24tZXhjaGFuZ2UvY29udGFpbmVycy91cGRhdGUtY3RzLXNldHRpbmctZGlhbG9nL3VwZGF0ZS1jdHMtc2V0dGluZy1kaWFsb2cuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSxnQkFBQTtFQUNBLGNBQUE7RUFDQSxjQUFBO0FBQ0Y7O0FBRUE7RUFDRSxjQUFBO0FBQ0Y7O0FBRUE7RUFDRSxnQkFBQTtFQUNBLGFBQUE7QUFDRjs7QUFDQTtFQUNFLFdBQUE7QUFFRjs7QUFDQTtFQUNJLGVBQUE7RUFDQSxXQUFBO0VBQ0EsaUJBQUE7RUFDQSxtQkFBQTtBQUVKOztBQUNBO0VBQ0ksZ0JBQUE7RUFDQSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxlQUFBO0FBRUo7O0FBQ0E7RUFDSSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxnQkFBQTtFQUNBLGtCQUFBO0FBRUo7O0FBQ0E7RUFDRSxtQkFBQTtFQUNBLGFBQUE7RUFDQSxzQkFBQTtBQUVGOztBQUNBO0VBQ0UsZ0JBQUE7RUFDQSxrQkFBQTtBQUVGOztBQUNBO0VBQ0UsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsU0FBQTtFQUNBLGtCQUFBO0FBRUY7O0FBQ0E7RUFDRSxhQUFBO0FBRUY7O0FBQ0E7RUFDRSxjQUFBO0VBQ0EsZ0JBQUE7RUFDQSxnQkFBQTtFQUNBLHVCQUFBO0VBQ0EsbUJBQUE7RUFDQSxXQUFBO0VBQ0EsZUFBQTtBQUVGOztBQUNBO0VBQ0UsV0FBQTtFQUNBLGtCQUFBO0FBRUY7QUFDQSx3cEZBQXdwRiIsInNvdXJjZXNDb250ZW50IjpbIi50aXRsZSB7XHJcbiAgZm9udC1zaXplOiAxLjNlbTtcclxuICBjb2xvcjogIzAwNzc5YjtcclxuICBkaXNwbGF5OiBibG9jaztcclxufVxyXG5cclxuLm1vZGFsLWFjdGlvbi1idXR0b24ge1xyXG4gIGZvbnQtc2l6ZTogMWVtO1xyXG59XHJcblxyXG4uYWN0aW9uLWJ1dHRvbnMge1xyXG4gIG1hcmdpbi10b3A6IDE2cHg7XHJcbiAgZGlzcGxheTogZmxleDsgXHJcbn1cclxuLnctMTAwIHtcclxuICB3aWR0aDogMTAwJTtcclxufVxyXG5cclxuLmxhc3QtdXBkYXRlZCB7XHJcbiAgICBmb250LXNpemU6IDEycHg7XHJcbiAgICBjb2xvcjogIzY2NjtcclxuICAgIG1hcmdpbi10b3A6IC0xMHB4O1xyXG4gICAgbWFyZ2luLWJvdHRvbTogMTBweDtcclxufVxyXG5cclxuLmZpbGUtbmFtZS1yb3cge1xyXG4gICAgbWluLWhlaWdodDogMjRweDtcclxuICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gICAgbWFyZ2luLXRvcDogMnB4O1xyXG59XHJcblxyXG4uYWxpZ25lZC1tZXNzYWdlIHtcclxuICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gICAgbWFyZ2luLWxlZnQ6IDhweDtcclxuICAgIG1hcmdpbi1ib3R0b206IDhweDtcclxufVxyXG5cclxuLmZpbGUtdXBsb2FkLWdyb3VwIHtcclxuICBtYXJnaW4tYm90dG9tOiAxNnB4O1xyXG4gIGRpc3BsYXk6IGZsZXg7XHJcbiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcclxufVxyXG5cclxuLmZpbGUtdXBsb2FkLWxhYmVsIHtcclxuICBmb250LXdlaWdodDogNTAwO1xyXG4gIG1hcmdpbi1ib3R0b206IDRweDtcclxufVxyXG5cclxuLmZpbGUtdXBsb2FkLXJvdyB7XHJcbiAgZGlzcGxheTogZmxleDtcclxuICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gIGdhcDogMTJweDtcclxuICBtYXJnaW4tYm90dG9tOiA0cHg7XHJcbn1cclxuXHJcbi5maWxlLWlucHV0IHtcclxuICBkaXNwbGF5OiBub25lO1xyXG59XHJcblxyXG4uZmlsZS1uYW1lIHtcclxuICBmbGV4OiAxIDEgYXV0bztcclxuICBtYXgtd2lkdGg6IDIwMHB4OyAvLyBsaW1pdCB3aWR0aCB0byBwcmV2ZW50IG92ZXJmbG93XHJcbiAgb3ZlcmZsb3c6IGhpZGRlbjtcclxuICB0ZXh0LW92ZXJmbG93OiBlbGxpcHNpcztcclxuICB3aGl0ZS1zcGFjZTogbm93cmFwO1xyXG4gIGNvbG9yOiAjMzMzO1xyXG4gIGN1cnNvcjogcG9pbnRlcjtcclxufVxyXG5cclxuLmZpbGUtcGxhY2Vob2xkZXIge1xyXG4gIGNvbG9yOiAjODg4O1xyXG4gIGZvbnQtc3R5bGU6IGl0YWxpYztcclxufVxyXG5cclxuIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "MAT_DIALOG_DATA", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ɵɵpipeBind2", "ctx_r1", "data", "systemUserPasswordUpdatedAt", "ɵɵproperty", "selectedFile", "name", "sftpSSHKeyUpdatedAt", "selectedCertificateFile", "ctsPublicCertificateUpdatedAt", "UpdateCtsSettingDialogComponent", "displaySftpSshKey", "key", "sftpSSHKey", "length", "substring", "displayCtsPublicCertificate", "cert", "ctsPublicCertificate", "constructor", "dialogRef", "<PERSON><PERSON><PERSON><PERSON>", "toasterService", "fb", "passwordVisible", "form", "group", "systemUserName", "required", "systemUserPassword", "sftpUserName", "sftpSshKey", "value", "disabled", "file", "certificateFile", "onFileChange", "event", "target", "files", "patchValue", "get", "updateValueAndValidity", "onCertificateFileChange", "endsWith", "warn", "life", "togglePasswordVisibility", "onCancel", "dirty", "fireDialog", "action", "title", "text", "type", "confirm", "close", "onSubmit", "invalid", "id", "ɵɵdirectiveInject", "i1", "MatDialogRef", "i2", "SweetAlertService", "i3", "ToasterService", "i4", "FormBuilder", "selectors", "decls", "vars", "consts", "template", "UpdateCtsSettingDialogComponent_Template", "rf", "ctx", "ɵɵlistener", "UpdateCtsSettingDialogComponent_Template_button_click_5_listener", "ɵɵrestoreView", "_r1", "ɵɵresetView", "ɵɵelement", "UpdateCtsSettingDialogComponent_Template_form_ngSubmit_7_listener", "UpdateCtsSettingDialogComponent_Template_button_click_20_listener", "ɵɵtemplate", "UpdateCtsSettingDialogComponent_div_24_Template", "UpdateCtsSettingDialogComponent_Template_button_click_41_listener", "fileInput_r3", "ɵɵreference", "click", "UpdateCtsSettingDialogComponent_span_45_Template", "UpdateCtsSettingDialogComponent_ng_template_46_Template", "ɵɵtemplateRefExtractor", "UpdateCtsSettingDialogComponent_Template_input_change_48_listener", "$event", "UpdateCtsSettingDialogComponent_div_51_Template", "UpdateCtsSettingDialogComponent_Template_button_click_63_listener", "certificateFileInput_r4", "UpdateCtsSettingDialogComponent_span_67_Template", "UpdateCtsSettingDialogComponent_ng_template_68_Template", "UpdateCtsSettingDialogComponent_Template_input_change_70_listener", "UpdateCtsSettingDialogComponent_div_73_Template", "UpdateCtsSettingDialogComponent_Template_button_click_75_listener", "ɵɵtextInterpolate", "noFile_r5", "noCertificateFile_r6"], "sources": ["C:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\src\\app\\features\\information-exchange\\containers\\update-cts-setting-dialog\\update-cts-setting-dialog.component.ts", "C:\\Temp\\azuregit\\cosmosDb\\ESS\\apps\\angular\\src\\app\\features\\information-exchange\\containers\\update-cts-setting-dialog\\update-cts-setting-dialog.component.html"], "sourcesContent": ["import { ToasterService } from '@abp/ng.theme.shared';\r\nimport { Component, Inject } from '@angular/core';\r\nimport { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';\r\nimport { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';\r\nimport { SweetAlertService } from '@app/shared/services/sweetalert.service';\r\nimport { BahamasCtsSettingDto } from 'proxies/proxies-ctsintegration-service/lib/proxy/bdo/ess/cts-integration/bahamas-cts-settings';\r\n\r\n@Component({\r\n  selector: 'app-update-cts-setting-dialog',\r\n  templateUrl: './update-cts-setting-dialog.component.html',\r\n  styleUrls: ['./update-cts-setting-dialog.component.scss']\r\n})\r\nexport class UpdateCtsSettingDialogComponent {\r\n  form: FormGroup;\r\n  selectedFile: File | null = null;\r\n  selectedCertificateFile: File | null = null;\r\n  passwordVisible = false;\r\n\r\n  get displaySftpSshKey(): string {\r\n    const key = this.data?.sftpSSHKey;\r\n    if (key && key.length > 30) {\r\n      return `${key.substring(0, 30)}...`;\r\n    }\r\n    return key || '';\r\n  }\r\n\r\n  get displayCtsPublicCertificate(): string {\r\n    const cert = this.data?.ctsPublicCertificate;\r\n    if (cert && cert.length > 30) {\r\n      return `${cert.substring(0, 30)}...`;\r\n    }\r\n    return cert || '';\r\n  }\r\n\r\n  constructor(\r\n    public dialogRef: MatDialogRef<UpdateCtsSettingDialogComponent>,\r\n    @Inject(MAT_DIALOG_DATA) public data: BahamasCtsSettingDto,\r\n    private sweetAlert: SweetAlertService,\r\n    private toasterService: ToasterService,\r\n    private fb: FormBuilder\r\n  ) {\r\n    this.form = this.fb.group({\r\n      systemUserName: [data?.systemUserName || '', Validators.required],\r\n      systemUserPassword: [data?.systemUserPassword, Validators.required],\r\n      sftpUserName: [data?.sftpUserName || '', Validators.required],\r\n      sftpSshKey: [{ value: this.displaySftpSshKey || '', disabled: true }],\r\n      ctsPublicCertificate: [{ value: this.displayCtsPublicCertificate || '', disabled: true }],\r\n      file: [null],\r\n      certificateFile: [null]\r\n    });\r\n  }\r\n\r\n  onFileChange(event: any) {\r\n    const file = event.target.files[0];\r\n    if (file) {\r\n      this.selectedFile = file;\r\n      this.form.patchValue({ file });\r\n      this.form.get('file')?.updateValueAndValidity();\r\n    } else {\r\n      this.selectedFile = null;\r\n      this.form.patchValue({ file: null });\r\n      this.form.get('file')?.updateValueAndValidity();\r\n    }\r\n  }\r\n\r\n  onCertificateFileChange(event: any) {\r\n    const file = event.target.files[0];\r\n    if (file) {\r\n      if (!file.name.endsWith('.crt') && !file.name.endsWith('.cer') && !file.name.endsWith('.txt')) {\r\n        this.selectedCertificateFile = null;\r\n        this.form.patchValue({ certificateFile: null });\r\n        this.form.get('certificateFile')?.updateValueAndValidity();\r\n        this.toasterService.warn('Only .crt, .cer, and .txt files are allowed.', null, { life: 7000 });\r\n        return;\r\n      }\r\n      this.selectedCertificateFile = file;\r\n      this.form.patchValue({ certificateFile: file });\r\n      this.form.get('certificateFile')?.updateValueAndValidity();\r\n    } else {\r\n      this.selectedCertificateFile = null;\r\n      this.form.patchValue({ certificateFile: null });\r\n      this.form.get('certificateFile')?.updateValueAndValidity();\r\n    }\r\n  }\r\n\r\n  togglePasswordVisibility() {\r\n    this.passwordVisible = !this.passwordVisible;\r\n  }\r\n\r\n  onCancel(): void {\r\n    if (this.form.dirty) {\r\n      this.sweetAlert.fireDialog({\r\n        action: \"delete\", title: \"Are you sure you want to close?\",\r\n        text: \"Any unsaved changes may be lost\", type: \"confirm\"\r\n      }, (confirm) => {\r\n        if (confirm) {\r\n          this.dialogRef.close();\r\n        }\r\n      });\r\n    } else {\r\n      this.dialogRef.close();\r\n    }\r\n  }\r\n\r\n  onSubmit(): void {\r\n    if (this.form.invalid) return;\r\n    this.dialogRef.close({\r\n      ...this.form.value,\r\n      id: this.data?.id,\r\n      file: this.selectedFile,\r\n      certificateFile: this.selectedCertificateFile\r\n    });\r\n  }\r\n}\r\n", "<div mat-dialog-title>\r\n    <div class=\"row\">\r\n        <div class=\"col-8 title\">CTS Settings</div>\r\n        <div class=\"col-4 text-end modal-action-button\">\r\n            <button type=\"button\" mat-raised-button class=\"ui-button\" (click)=\"onCancel()\">\r\n                <i class=\"fas fa-times\"></i>\r\n            </button>\r\n        </div>\r\n    </div>\r\n</div>\r\n<form [formGroup]=\"form\" (ngSubmit)=\"onSubmit()\">\r\n    <mat-dialog-content>\r\n        <div class=\"row\">\r\n            <div class=\"col-md-4\">\r\n                <mat-form-field class=\"w-100\">\r\n                    <mat-label>System User Name</mat-label>\r\n                    <input matInput formControlName=\"systemUserName\" required>\r\n                </mat-form-field>\r\n            </div>\r\n            <div class=\"col-md-4\">\r\n                <mat-form-field class=\"w-100\">\r\n                    <mat-label>System User Password</mat-label>\r\n                    <input matInput [type]=\"passwordVisible ? 'text' : 'password'\" formControlName=\"systemUserPassword\"\r\n                        required>\r\n                    <button mat-icon-button matSuffix (click)=\"togglePasswordVisibility()\" type=\"button\">\r\n                        <mat-icon>{{passwordVisible ? 'visibility_off' : 'visibility'}}</mat-icon>\r\n                    </button>\r\n                </mat-form-field>\r\n            </div>\r\n        </div>\r\n        <div class=\"row\">\r\n            <div *ngIf=\"data?.systemUserPasswordUpdatedAt\" class=\"last-updated\">\r\n                System User Password last updated at: {{ data?.systemUserPasswordUpdatedAt | date:'dd/MM/yyyy' }}\r\n            </div>\r\n        </div>\r\n        <div class=\"row\">\r\n            <div class=\"col-md-4\">\r\n                <mat-form-field class=\"w-100\">\r\n                    <mat-label>SFTP User Name</mat-label>\r\n                    <input matInput formControlName=\"sftpUserName\" required>\r\n                </mat-form-field>\r\n            </div>\r\n            <div class=\"col-md-4\">\r\n                <mat-form-field class=\"w-100\">\r\n                    <mat-label>SFTP SSH Key</mat-label>\r\n                    <input matInput formControlName=\"sftpSshKey\">\r\n                </mat-form-field>\r\n            </div>\r\n            <div class=\"col-md-4\">\r\n                <div class=\"file-upload-group\">\r\n                    <label class=\"file-upload-label\">Upload New SSH Key File</label>\r\n                    <div class=\"file-upload-row\">\r\n                        <button mat-stroked-button color=\"primary\" type=\"button\" (click)=\"fileInput.click()\">\r\n                            <mat-icon>upload_file</mat-icon>\r\n                            Choose File\r\n                        </button>\r\n                        <span class=\"file-name\" *ngIf=\"selectedFile; else noFile\" [matTooltip]=\"selectedFile.name\">\r\n                            {{ selectedFile.name }}\r\n                        </span>\r\n                        <ng-template #noFile>\r\n                            <span class=\"file-placeholder\">No file chosen</span>\r\n                        </ng-template>\r\n                    </div>\r\n                    <input #fileInput accept=\"*\" type=\"file\" formControlName=\"file\" (change)=\"onFileChange($event)\" class=\"file-input\" />\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"row\">\r\n            <div *ngIf=\"data?.sftpSSHKeyUpdatedAt\" class=\"last-updated\">\r\n                SFTP SSH Key last updated at: {{ data?.sftpSSHKeyUpdatedAt | date:'dd/MM/yyyy' }}\r\n            </div>\r\n        </div>\r\n        <div class=\"row\">\r\n            <div class=\"col-md-4\">\r\n                <mat-form-field class=\"w-100\">\r\n                    <mat-label>CTS Public Certificate</mat-label>\r\n                    <input matInput formControlName=\"ctsPublicCertificate\">\r\n                </mat-form-field>\r\n            </div>\r\n            <div class=\"col-md-4\">\r\n                <div class=\"file-upload-group\">\r\n                    <label class=\"file-upload-label\">Upload New CTS Public Certificate File</label>\r\n                    <div class=\"file-upload-row\">\r\n                        <button mat-stroked-button color=\"primary\" type=\"button\" (click)=\"certificateFileInput.click()\">\r\n                            <mat-icon>upload_file</mat-icon>\r\n                            Choose File\r\n                        </button>\r\n                        <span class=\"file-name\" *ngIf=\"selectedCertificateFile; else noCertificateFile\" [matTooltip]=\"selectedCertificateFile.name\">\r\n                            {{ selectedCertificateFile.name }}\r\n                        </span>\r\n                        <ng-template #noCertificateFile>\r\n                            <span class=\"file-placeholder\">No file chosen</span>\r\n                        </ng-template>\r\n                    </div>\r\n                    <input #certificateFileInput accept=\".crt,.cer,.txt\" type=\"file\" formControlName=\"certificateFile\" (change)=\"onCertificateFileChange($event)\" class=\"file-input\" />\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"row\">\r\n            <div *ngIf=\"data?.ctsPublicCertificateUpdatedAt\" class=\"last-updated\">\r\n                CTS Public Certificate last updated at: {{ data?.ctsPublicCertificateUpdatedAt | date:'dd/MM/yyyy' }}\r\n            </div>\r\n        </div>\r\n    </mat-dialog-content>\r\n    <mat-dialog-actions align=\"end\">        \r\n        <button mat-raised-button type=\"button\" (click)=\"onCancel()\">Cancel</button>\r\n        <button mat-raised-button class=\"ui-button\" type=\"submit\" [disabled]=\"form.invalid\">Save</button>\r\n    </mat-dialog-actions>\r\n</form>"], "mappings": "AAEA,SAAiCA,UAAU,QAAQ,gBAAgB;AACnE,SAAuBC,eAAe,QAAQ,0BAA0B;;;;;;;;;;;;;;;IC4B5DC,EAAA,CAAAC,cAAA,cAAoE;IAChED,EAAA,CAAAE,MAAA,GACJ;;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,EACJ;IADIJ,EAAA,CAAAK,kBAAA,4CAAAL,EAAA,CAAAM,WAAA,OAAAC,MAAA,CAAAC,IAAA,kBAAAD,MAAA,CAAAC,IAAA,CAAAC,2BAAA,qBACJ;;;;;IAuBYT,EAAA,CAAAC,cAAA,eAA2F;IACvFD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAFmDH,EAAA,CAAAU,UAAA,eAAAH,MAAA,CAAAI,YAAA,CAAAC,IAAA,CAAgC;IACtFZ,EAAA,CAAAI,SAAA,EACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAE,MAAA,CAAAI,YAAA,CAAAC,IAAA,MACJ;;;;;IAEIZ,EAAA,CAAAC,cAAA,eAA+B;IAAAD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAQpEH,EAAA,CAAAC,cAAA,cAA4D;IACxDD,EAAA,CAAAE,MAAA,GACJ;;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,EACJ;IADIJ,EAAA,CAAAK,kBAAA,oCAAAL,EAAA,CAAAM,WAAA,OAAAC,MAAA,CAAAC,IAAA,kBAAAD,MAAA,CAAAC,IAAA,CAAAK,mBAAA,qBACJ;;;;;IAiBYb,EAAA,CAAAC,cAAA,eAA4H;IACxHD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAFyEH,EAAA,CAAAU,UAAA,eAAAH,MAAA,CAAAO,uBAAA,CAAAF,IAAA,CAA2C;IACvHZ,EAAA,CAAAI,SAAA,EACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAE,MAAA,CAAAO,uBAAA,CAAAF,IAAA,MACJ;;;;;IAEIZ,EAAA,CAAAC,cAAA,eAA+B;IAAAD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAQpEH,EAAA,CAAAC,cAAA,cAAsE;IAClED,EAAA,CAAAE,MAAA,GACJ;;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,EACJ;IADIJ,EAAA,CAAAK,kBAAA,8CAAAL,EAAA,CAAAM,WAAA,OAAAC,MAAA,CAAAC,IAAA,kBAAAD,MAAA,CAAAC,IAAA,CAAAO,6BAAA,qBACJ;;;ADzFZ,OAAM,MAAOC,+BAA+B;EAM1C,IAAIC,iBAAiBA,CAAA;IACnB,MAAMC,GAAG,GAAG,IAAI,CAACV,IAAI,EAAEW,UAAU;IACjC,IAAID,GAAG,IAAIA,GAAG,CAACE,MAAM,GAAG,EAAE,EAAE;MAC1B,OAAO,GAAGF,GAAG,CAACG,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK;IACrC;IACA,OAAOH,GAAG,IAAI,EAAE;EAClB;EAEA,IAAII,2BAA2BA,CAAA;IAC7B,MAAMC,IAAI,GAAG,IAAI,CAACf,IAAI,EAAEgB,oBAAoB;IAC5C,IAAID,IAAI,IAAIA,IAAI,CAACH,MAAM,GAAG,EAAE,EAAE;MAC5B,OAAO,GAAGG,IAAI,CAACF,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK;IACtC;IACA,OAAOE,IAAI,IAAI,EAAE;EACnB;EAEAE,YACSC,SAAwD,EAC/BlB,IAA0B,EAClDmB,UAA6B,EAC7BC,cAA8B,EAC9BC,EAAe;IAJhB,KAAAH,SAAS,GAATA,SAAS;IACgB,KAAAlB,IAAI,GAAJA,IAAI;IAC5B,KAAAmB,UAAU,GAAVA,UAAU;IACV,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,EAAE,GAAFA,EAAE;IAzBZ,KAAAlB,YAAY,GAAgB,IAAI;IAChC,KAAAG,uBAAuB,GAAgB,IAAI;IAC3C,KAAAgB,eAAe,GAAG,KAAK;IAyBrB,IAAI,CAACC,IAAI,GAAG,IAAI,CAACF,EAAE,CAACG,KAAK,CAAC;MACxBC,cAAc,EAAE,CAACzB,IAAI,EAAEyB,cAAc,IAAI,EAAE,EAAEnC,UAAU,CAACoC,QAAQ,CAAC;MACjEC,kBAAkB,EAAE,CAAC3B,IAAI,EAAE2B,kBAAkB,EAAErC,UAAU,CAACoC,QAAQ,CAAC;MACnEE,YAAY,EAAE,CAAC5B,IAAI,EAAE4B,YAAY,IAAI,EAAE,EAAEtC,UAAU,CAACoC,QAAQ,CAAC;MAC7DG,UAAU,EAAE,CAAC;QAAEC,KAAK,EAAE,IAAI,CAACrB,iBAAiB,IAAI,EAAE;QAAEsB,QAAQ,EAAE;MAAI,CAAE,CAAC;MACrEf,oBAAoB,EAAE,CAAC;QAAEc,KAAK,EAAE,IAAI,CAAChB,2BAA2B,IAAI,EAAE;QAAEiB,QAAQ,EAAE;MAAI,CAAE,CAAC;MACzFC,IAAI,EAAE,CAAC,IAAI,CAAC;MACZC,eAAe,EAAE,CAAC,IAAI;KACvB,CAAC;EACJ;EAEAC,YAAYA,CAACC,KAAU;IACrB,MAAMH,IAAI,GAAGG,KAAK,CAACC,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAClC,IAAIL,IAAI,EAAE;MACR,IAAI,CAAC7B,YAAY,GAAG6B,IAAI;MACxB,IAAI,CAACT,IAAI,CAACe,UAAU,CAAC;QAAEN;MAAI,CAAE,CAAC;MAC9B,IAAI,CAACT,IAAI,CAACgB,GAAG,CAAC,MAAM,CAAC,EAAEC,sBAAsB,EAAE;IACjD,CAAC,MAAM;MACL,IAAI,CAACrC,YAAY,GAAG,IAAI;MACxB,IAAI,CAACoB,IAAI,CAACe,UAAU,CAAC;QAAEN,IAAI,EAAE;MAAI,CAAE,CAAC;MACpC,IAAI,CAACT,IAAI,CAACgB,GAAG,CAAC,MAAM,CAAC,EAAEC,sBAAsB,EAAE;IACjD;EACF;EAEAC,uBAAuBA,CAACN,KAAU;IAChC,MAAMH,IAAI,GAAGG,KAAK,CAACC,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAClC,IAAIL,IAAI,EAAE;MACR,IAAI,CAACA,IAAI,CAAC5B,IAAI,CAACsC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAACV,IAAI,CAAC5B,IAAI,CAACsC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAACV,IAAI,CAAC5B,IAAI,CAACsC,QAAQ,CAAC,MAAM,CAAC,EAAE;QAC7F,IAAI,CAACpC,uBAAuB,GAAG,IAAI;QACnC,IAAI,CAACiB,IAAI,CAACe,UAAU,CAAC;UAAEL,eAAe,EAAE;QAAI,CAAE,CAAC;QAC/C,IAAI,CAACV,IAAI,CAACgB,GAAG,CAAC,iBAAiB,CAAC,EAAEC,sBAAsB,EAAE;QAC1D,IAAI,CAACpB,cAAc,CAACuB,IAAI,CAAC,8CAA8C,EAAE,IAAI,EAAE;UAAEC,IAAI,EAAE;QAAI,CAAE,CAAC;QAC9F;MACF;MACA,IAAI,CAACtC,uBAAuB,GAAG0B,IAAI;MACnC,IAAI,CAACT,IAAI,CAACe,UAAU,CAAC;QAAEL,eAAe,EAAED;MAAI,CAAE,CAAC;MAC/C,IAAI,CAACT,IAAI,CAACgB,GAAG,CAAC,iBAAiB,CAAC,EAAEC,sBAAsB,EAAE;IAC5D,CAAC,MAAM;MACL,IAAI,CAAClC,uBAAuB,GAAG,IAAI;MACnC,IAAI,CAACiB,IAAI,CAACe,UAAU,CAAC;QAAEL,eAAe,EAAE;MAAI,CAAE,CAAC;MAC/C,IAAI,CAACV,IAAI,CAACgB,GAAG,CAAC,iBAAiB,CAAC,EAAEC,sBAAsB,EAAE;IAC5D;EACF;EAEAK,wBAAwBA,CAAA;IACtB,IAAI,CAACvB,eAAe,GAAG,CAAC,IAAI,CAACA,eAAe;EAC9C;EAEAwB,QAAQA,CAAA;IACN,IAAI,IAAI,CAACvB,IAAI,CAACwB,KAAK,EAAE;MACnB,IAAI,CAAC5B,UAAU,CAAC6B,UAAU,CAAC;QACzBC,MAAM,EAAE,QAAQ;QAAEC,KAAK,EAAE,iCAAiC;QAC1DC,IAAI,EAAE,iCAAiC;QAAEC,IAAI,EAAE;OAChD,EAAGC,OAAO,IAAI;QACb,IAAIA,OAAO,EAAE;UACX,IAAI,CAACnC,SAAS,CAACoC,KAAK,EAAE;QACxB;MACF,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,IAAI,CAACpC,SAAS,CAACoC,KAAK,EAAE;IACxB;EACF;EAEAC,QAAQA,CAAA;IACN,IAAI,IAAI,CAAChC,IAAI,CAACiC,OAAO,EAAE;IACvB,IAAI,CAACtC,SAAS,CAACoC,KAAK,CAAC;MACnB,GAAG,IAAI,CAAC/B,IAAI,CAACO,KAAK;MAClB2B,EAAE,EAAE,IAAI,CAACzD,IAAI,EAAEyD,EAAE;MACjBzB,IAAI,EAAE,IAAI,CAAC7B,YAAY;MACvB8B,eAAe,EAAE,IAAI,CAAC3B;KACvB,CAAC;EACJ;;;uBApGWE,+BAA+B,EAAAhB,EAAA,CAAAkE,iBAAA,CAAAC,EAAA,CAAAC,YAAA,GAAApE,EAAA,CAAAkE,iBAAA,CAwBhCnE,eAAe,GAAAC,EAAA,CAAAkE,iBAAA,CAAAG,EAAA,CAAAC,iBAAA,GAAAtE,EAAA,CAAAkE,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAAxE,EAAA,CAAAkE,iBAAA,CAAAO,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAxBd1D,+BAA+B;MAAA2D,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UCVpCjF,EAFR,CAAAC,cAAA,aAAsB,aACD,aACY;UAAAD,EAAA,CAAAE,MAAA,mBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAEvCH,EADJ,CAAAC,cAAA,aAAgD,gBACmC;UAArBD,EAAA,CAAAmF,UAAA,mBAAAC,iEAAA;YAAApF,EAAA,CAAAqF,aAAA,CAAAC,GAAA;YAAA,OAAAtF,EAAA,CAAAuF,WAAA,CAASL,GAAA,CAAA5B,QAAA,EAAU;UAAA,EAAC;UAC1EtD,EAAA,CAAAwF,SAAA,WAA4B;UAI5CxF,EAHY,CAAAG,YAAA,EAAS,EACP,EACJ,EACJ;UACNH,EAAA,CAAAC,cAAA,eAAiD;UAAxBD,EAAA,CAAAmF,UAAA,sBAAAM,kEAAA;YAAAzF,EAAA,CAAAqF,aAAA,CAAAC,GAAA;YAAA,OAAAtF,EAAA,CAAAuF,WAAA,CAAYL,GAAA,CAAAnB,QAAA,EAAU;UAAA,EAAC;UAK5B/D,EAJhB,CAAAC,cAAA,yBAAoB,aACC,eACS,0BACY,iBACf;UAAAD,EAAA,CAAAE,MAAA,wBAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACvCH,EAAA,CAAAwF,SAAA,iBAA0D;UAElExF,EADI,CAAAG,YAAA,EAAiB,EACf;UAGEH,EAFR,CAAAC,cAAA,eAAsB,0BACY,iBACf;UAAAD,EAAA,CAAAE,MAAA,4BAAoB;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC3CH,EAAA,CAAAwF,SAAA,iBACa;UACbxF,EAAA,CAAAC,cAAA,kBAAqF;UAAnDD,EAAA,CAAAmF,UAAA,mBAAAO,kEAAA;YAAA1F,EAAA,CAAAqF,aAAA,CAAAC,GAAA;YAAA,OAAAtF,EAAA,CAAAuF,WAAA,CAASL,GAAA,CAAA7B,wBAAA,EAA0B;UAAA,EAAC;UAClErD,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,IAAqD;UAI/EF,EAJ+E,CAAAG,YAAA,EAAW,EACrE,EACI,EACf,EACJ;UACNH,EAAA,CAAAC,cAAA,cAAiB;UACbD,EAAA,CAAA2F,UAAA,KAAAC,+CAAA,kBAAoE;UAGxE5F,EAAA,CAAAG,YAAA,EAAM;UAIMH,EAHZ,CAAAC,cAAA,cAAiB,eACS,0BACY,iBACf;UAAAD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACrCH,EAAA,CAAAwF,SAAA,iBAAwD;UAEhExF,EADI,CAAAG,YAAA,EAAiB,EACf;UAGEH,EAFR,CAAAC,cAAA,eAAsB,0BACY,iBACf;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACnCH,EAAA,CAAAwF,SAAA,iBAA6C;UAErDxF,EADI,CAAAG,YAAA,EAAiB,EACf;UAGEH,EAFR,CAAAC,cAAA,eAAsB,eACa,iBACM;UAAAD,EAAA,CAAAE,MAAA,+BAAuB;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAE5DH,EADJ,CAAAC,cAAA,eAA6B,kBAC4D;UAA5BD,EAAA,CAAAmF,UAAA,mBAAAU,kEAAA;YAAA7F,EAAA,CAAAqF,aAAA,CAAAC,GAAA;YAAA,MAAAQ,YAAA,GAAA9F,EAAA,CAAA+F,WAAA;YAAA,OAAA/F,EAAA,CAAAuF,WAAA,CAASO,YAAA,CAAAE,KAAA,EAAiB;UAAA,EAAC;UAChFhG,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAChCH,EAAA,CAAAE,MAAA,qBACJ;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAITH,EAHA,CAAA2F,UAAA,KAAAM,gDAAA,mBAA2F,KAAAC,uDAAA,gCAAAlG,EAAA,CAAAmG,sBAAA,CAGtE;UAGzBnG,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,oBAAqH;UAArDD,EAAA,CAAAmF,UAAA,oBAAAiB,kEAAAC,MAAA;YAAArG,EAAA,CAAAqF,aAAA,CAAAC,GAAA;YAAA,OAAAtF,EAAA,CAAAuF,WAAA,CAAUL,GAAA,CAAAxC,YAAA,CAAA2D,MAAA,CAAoB;UAAA,EAAC;UAG3GrG,EAHY,CAAAG,YAAA,EAAqH,EACnH,EACJ,EACJ;UACNH,EAAA,CAAAC,cAAA,cAAiB;UACbD,EAAA,CAAA2F,UAAA,KAAAW,+CAAA,kBAA4D;UAGhEtG,EAAA,CAAAG,YAAA,EAAM;UAIMH,EAHZ,CAAAC,cAAA,cAAiB,eACS,0BACY,iBACf;UAAAD,EAAA,CAAAE,MAAA,8BAAsB;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC7CH,EAAA,CAAAwF,SAAA,iBAAuD;UAE/DxF,EADI,CAAAG,YAAA,EAAiB,EACf;UAGEH,EAFR,CAAAC,cAAA,eAAsB,eACa,iBACM;UAAAD,EAAA,CAAAE,MAAA,8CAAsC;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAE3EH,EADJ,CAAAC,cAAA,eAA6B,kBACuE;UAAvCD,EAAA,CAAAmF,UAAA,mBAAAoB,kEAAA;YAAAvG,EAAA,CAAAqF,aAAA,CAAAC,GAAA;YAAA,MAAAkB,uBAAA,GAAAxG,EAAA,CAAA+F,WAAA;YAAA,OAAA/F,EAAA,CAAAuF,WAAA,CAASiB,uBAAA,CAAAR,KAAA,EAA4B;UAAA,EAAC;UAC3FhG,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAChCH,EAAA,CAAAE,MAAA,qBACJ;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAITH,EAHA,CAAA2F,UAAA,KAAAc,gDAAA,mBAA4H,KAAAC,uDAAA,gCAAA1G,EAAA,CAAAmG,sBAAA,CAG5F;UAGpCnG,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,oBAAmK;UAAhED,EAAA,CAAAmF,UAAA,oBAAAwB,kEAAAN,MAAA;YAAArG,EAAA,CAAAqF,aAAA,CAAAC,GAAA;YAAA,OAAAtF,EAAA,CAAAuF,WAAA,CAAUL,GAAA,CAAAjC,uBAAA,CAAAoD,MAAA,CAA+B;UAAA,EAAC;UAGzJrG,EAHY,CAAAG,YAAA,EAAmK,EACjK,EACJ,EACJ;UACNH,EAAA,CAAAC,cAAA,cAAiB;UACbD,EAAA,CAAA2F,UAAA,KAAAiB,+CAAA,kBAAsE;UAI9E5G,EADI,CAAAG,YAAA,EAAM,EACW;UAEjBH,EADJ,CAAAC,cAAA,8BAAgC,kBACiC;UAArBD,EAAA,CAAAmF,UAAA,mBAAA0B,kEAAA;YAAA7G,EAAA,CAAAqF,aAAA,CAAAC,GAAA;YAAA,OAAAtF,EAAA,CAAAuF,WAAA,CAASL,GAAA,CAAA5B,QAAA,EAAU;UAAA,EAAC;UAACtD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC5EH,EAAA,CAAAC,cAAA,kBAAoF;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAEhGF,EAFgG,CAAAG,YAAA,EAAS,EAChF,EAClB;;;;;UAlGDH,EAAA,CAAAI,SAAA,GAAkB;UAAlBJ,EAAA,CAAAU,UAAA,cAAAwE,GAAA,CAAAnD,IAAA,CAAkB;UAYY/B,EAAA,CAAAI,SAAA,IAA8C;UAA9CJ,EAAA,CAAAU,UAAA,SAAAwE,GAAA,CAAApD,eAAA,uBAA8C;UAGhD9B,EAAA,CAAAI,SAAA,GAAqD;UAArDJ,EAAA,CAAA8G,iBAAA,CAAA5B,GAAA,CAAApD,eAAA,mCAAqD;UAMrE9B,EAAA,CAAAI,SAAA,GAAuC;UAAvCJ,EAAA,CAAAU,UAAA,SAAAwE,GAAA,CAAA1E,IAAA,kBAAA0E,GAAA,CAAA1E,IAAA,CAAAC,2BAAA,CAAuC;UAyBRT,EAAA,CAAAI,SAAA,IAAoB;UAAAJ,EAApB,CAAAU,UAAA,SAAAwE,GAAA,CAAAvE,YAAA,CAAoB,aAAAoG,SAAA,CAAW;UAY9D/G,EAAA,CAAAI,SAAA,GAA+B;UAA/BJ,EAAA,CAAAU,UAAA,SAAAwE,GAAA,CAAA1E,IAAA,kBAAA0E,GAAA,CAAA1E,IAAA,CAAAK,mBAAA,CAA+B;UAmBAb,EAAA,CAAAI,SAAA,IAA+B;UAAAJ,EAA/B,CAAAU,UAAA,SAAAwE,GAAA,CAAApE,uBAAA,CAA+B,aAAAkG,oBAAA,CAAsB;UAYpFhH,EAAA,CAAAI,SAAA,GAAyC;UAAzCJ,EAAA,CAAAU,UAAA,SAAAwE,GAAA,CAAA1E,IAAA,kBAAA0E,GAAA,CAAA1E,IAAA,CAAAO,6BAAA,CAAyC;UAOOf,EAAA,CAAAI,SAAA,GAAyB;UAAzBJ,EAAA,CAAAU,UAAA,aAAAwE,GAAA,CAAAnD,IAAA,CAAAiC,OAAA,CAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}