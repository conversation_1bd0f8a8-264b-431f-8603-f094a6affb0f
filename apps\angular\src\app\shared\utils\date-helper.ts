import { utcToZonedTime, format } from 'date-fns-tz';
export class DateHelper {

    static formatEstUtcDate(dateStr: string, formatStr:string): string {
        if (!dateStr) return '';
        if (dateStr.length === 10) return dateStr; //avoid this method called twice
        const dt=  utcToZonedTime(dateStr, 'America/New_York');
        return format(dt,formatStr);
    }

    /**
     * Converts UTC date string to user's browser local time and formats it
     * @param dateStr UTC date string (ISO format)
     * @param formatStr Format string for date-fns (e.g., 'dd/MM/yyyy', 'yyyy-MM-dd HH:mm')
     * @returns Formatted date string in user's local timezone
     */
    static formatUtcToLocalDate(dateStr: string, formatStr: string): string {
        if (!dateStr) return '';
        if (dateStr.length === 10) return dateStr; // avoid this method called twice for date-only strings

        // Get user's browser timezone
        const userTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

        // Convert UTC to user's local timezone
        const localDateTime = utcToZonedTime(dateStr, userTimezone);

        // Format and return
        return format(localDateTime, formatStr, { timeZone: userTimezone });
    }
}