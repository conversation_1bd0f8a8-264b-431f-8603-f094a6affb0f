﻿using Bdo.Ess.CtsIntegration.Constants;
using Bdo.Ess.CtsIntegration.Entities;
using Bdo.Ess.CtsIntegration.enums;
using Renci.SshNet.Messages;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Bdo.Ess.CtsIntegration.StateMachine
{
    public static class DataPacketStateMachine
    {
        private static readonly Dictionary<(CTSUploadStatus, DataPacketEvent), CTSUploadStatus> Transitions = InitializeTransitions();
        private static readonly Dictionary<CTSUploadStatus, List<DataPacketAction>> AvailableActions = InitializeAvailableActions();

  
        private static Dictionary<(CTSUploadStatus, DataPacketEvent), CTSUploadStatus> InitializeTransitions()
        {
            return new Dictionary<(CTSUploadStatus, DataPacketEvent), CTSUploadStatus>
            {
                // From initial GenerationInProgress state (after data packet generation is triggered)
                { (CTSUploadStatus.GenerationInProgress, DataPacketEvent.CountryNotEnrolled), CTSUploadStatus.ReceivingCountryNotEnrolled },
                { (CTSUploadStatus.GenerationInProgress, DataPacketEvent.GenerationFailed), CTSUploadStatus.DataPacketGenerationFailed },
                { (CTSUploadStatus.GenerationInProgress, DataPacketEvent.GenerationSuccess), CTSUploadStatus.NotStarted },
                
                // From ReceivingCountryNotEnrolled (which means country is not enrolled in CTS)
                { (CTSUploadStatus.ReceivingCountryNotEnrolled, DataPacketEvent.StartRegeneration), CTSUploadStatus.GenerationInProgress },
                
                // From generation failed
                { (CTSUploadStatus.DataPacketGenerationFailed, DataPacketEvent.StartRegeneration), CTSUploadStatus.GenerationInProgress },

                // From NotStarted (which means ready to upload to CTS)
                { (CTSUploadStatus.NotStarted, DataPacketEvent.MarkAsDoNotUpload), CTSUploadStatus.DoNotUpload },
                { (CTSUploadStatus.NotStarted, DataPacketEvent.StartUpload), CTSUploadStatus.Uploading },
                { (CTSUploadStatus.NotStarted, DataPacketEvent.StartRegeneration), CTSUploadStatus.GenerationInProgress },
                
                // From DoNotUpload
                { (CTSUploadStatus.DoNotUpload, DataPacketEvent.UnmarkAsDoNotUpload), CTSUploadStatus.NotStarted },

                // From uploading
                { (CTSUploadStatus.Uploading, DataPacketEvent.UploadCompleted), CTSUploadStatus.Uploaded },
                { (CTSUploadStatus.Uploading, DataPacketEvent.UploadFailed), CTSUploadStatus.UploadFailed },
                
                // From upload failed
                { (CTSUploadStatus.UploadFailed, DataPacketEvent.StartRegeneration), CTSUploadStatus.GenerationInProgress },

                // From Uploaded, conditional allow Re-Generation
                { (CTSUploadStatus.Uploaded, DataPacketEvent.StartRegeneration), CTSUploadStatus.GenerationInProgress },
            };
        }

        private static Dictionary<CTSUploadStatus, List<DataPacketAction>> InitializeAvailableActions()
        {
            return new Dictionary<CTSUploadStatus, List<DataPacketAction>>
            {
                {
                    CTSUploadStatus.GenerationInProgress,
                    new List<DataPacketAction>() // No actions available during generation
                },
                {
                    CTSUploadStatus.NotStarted,
                    new List<DataPacketAction>
                    {
                        DataPacketAction.Upload,
                        DataPacketAction.Regenerate,
                        DataPacketAction.MarkDoNotUpload
                    }
                },
                {
                    CTSUploadStatus.Uploading,
                    new List<DataPacketAction>() // No actions available during upload
                },
                {
                    CTSUploadStatus.Uploaded,
                    new List<DataPacketAction>
                    {
                        DataPacketAction.Regenerate,  //Conditional Allows (based on transmission status)
                        DataPacketAction.CheckTransmissionStatus
                    }
                },
                {
                    CTSUploadStatus.UploadFailed,
                    new List<DataPacketAction>
                    {
                        DataPacketAction.Regenerate
                    }
                },
                
                {
                    CTSUploadStatus.ReceivingCountryNotEnrolled,
                    new List<DataPacketAction>
                    {
                        DataPacketAction.Regenerate
                    }
                },
                {
                    CTSUploadStatus.DataPacketGenerationFailed,
                    new List<DataPacketAction>
                    {
                        DataPacketAction.Regenerate
                    }
                },
                {
                    CTSUploadStatus.DoNotUpload,
                    new List<DataPacketAction>
                    {
                        DataPacketAction.UnmarkDoNotUpload
                    }
                }
            };
        }

        public static StateTransitionResult TriggerEvent(CtsPackageRequest packageRequest, DataPacketEvent eventType)
        {
            var currentState = packageRequest.UploadStatus ?? CTSUploadStatus.GenerationInProgress;
            var transitionKey = (currentState, eventType);

            //TransmissionStatus wouldn't impact state transition, so we don't include it in the key
            if (!Transitions.ContainsKey(transitionKey) && eventType != DataPacketEvent.TransmissionStatusReceived)
            {
                return new StateTransitionResult
                {
                    IsSuccess = false,
                    Message = $"Invalid transition from {currentState} with event {eventType}",
                    NewState = currentState,
                    AvailableActions = GetAvailableActions(packageRequest.UploadStatus,packageRequest.TransmissionStatus, packageRequest.UploadedAt)
                };
            }

           
            var newState = Transitions[transitionKey];

            if (eventType == DataPacketEvent.StartUpload)
            {
                packageRequest.UploadedAt = null;
            }
            if (eventType == DataPacketEvent.UploadCompleted)
            {
                packageRequest.EligibleCheckCtsStatus = true;
                packageRequest.UploadedAt = DateTime.UtcNow;
            }
            else if (eventType == DataPacketEvent.StartRegeneration)
            {
                packageRequest.CtsPackageFileName = null; // Reset package file name on regeneration
                packageRequest.PackageZipUrl = null; // Reset package zip URL on regeneration
                packageRequest.FileCreatedAt = null; // Reset file creation date on regeneration
                packageRequest.UploadedAt = null; // Reset uploaded date on regeneration
                packageRequest.EligibleCheckCtsStatus = false; // Reset CTS status check eligibility
                packageRequest.IsExcludeCtsUpload = false; // Reset exclusion flag for CTS upload
                packageRequest.StatusUpdatedAt = null; // Reset status updated date on regeneration
                packageRequest.TransmissionStatus = "";
                packageRequest.ProcessInfo = null; // Reset process info on regeneration
            }
            else if (eventType == DataPacketEvent.MarkAsDoNotUpload)
            {
                packageRequest.IsExcludeCtsUpload = true;
            }
            else if(eventType == DataPacketEvent.UnmarkAsDoNotUpload)
            {
                packageRequest.IsExcludeCtsUpload = false;
            }
            else if(eventType == DataPacketEvent.TransmissionStatusReceived)
            {
                if (ShouldRefreshTransmissionStatus(packageRequest))
                {
                    packageRequest.EligibleCheckCtsStatus = true;
                }
                else
                {
                    packageRequest.EligibleCheckCtsStatus = false;
                }
               
            }
            
            packageRequest.UploadStatus = newState;
            packageRequest.StatusUpdatedAt = DateTime.UtcNow;

            return new StateTransitionResult
            {
                IsSuccess = true,
                Message = $"Transitioned from {currentState} to {newState} with event {eventType}",
                NewState = newState,
                AvailableActions = GetAvailableActions(packageRequest.UploadStatus, packageRequest.TransmissionStatus, packageRequest.UploadedAt)
            };
        }

        public static List<DataPacketAction> GetAvailableActions(CTSUploadStatus? uploadStatus, string? transmissionStatus, DateTime? uploadedAt)
        {
            var statusToCheck = uploadStatus ?? CTSUploadStatus.GenerationInProgress;
            var availableStatus = AvailableActions.TryGetValue(statusToCheck, out List<DataPacketAction>? value)
                ? value : [];
            //Always allows regneration for uploaded status
            /*if (statusToCheck == CTSUploadStatus.Uploaded && !AllowConditionalRegeneration(uploadStatus, transmissionStatus))
            {
                availableStatus.Remove(DataPacketAction.Regenerate);
            }
            
            if (statusToCheck == CTSUploadStatus.Uploaded && IsTransmissionFinal(uploadStatus, transmissionStatus, uploadedAt))
            {
                availableStatus.Remove(DataPacketAction.Regenerate);
            }*/
            return availableStatus;
        }

        public static bool CanPerformAction(CtsPackageRequest packageRequest, DataPacketAction action)
        {
            var availableActions = GetAvailableActions(packageRequest.UploadStatus, packageRequest.TransmissionStatus, packageRequest.UploadedAt);
            return availableActions.Contains(action);
        }

        public static bool IsTransmissionFinal(CtsPackageRequest packageRequest)
        {
            return IsTransmissionFinal(packageRequest.UploadStatus, packageRequest.TransmissionStatus, packageRequest.UploadedAt);
        }

        public static bool IsTransmissionFinal(CTSUploadStatus? uploadStatus, string? transmissionStatus, DateTime? uploadedAt)
        {
            transmissionStatus = transmissionStatus ?? "";
            var intermStatus = new List<string>{ CtsTransmissionStatus.RC001.ToString(), CtsTransmissionStatus.RC007.ToString()
               , CtsTransmissionStatus.RC021.ToString(), CtsTransmissionStatus.RC022.ToString() };

            if (uploadStatus != CTSUploadStatus.Uploaded) return false;
            var isFinal = !intermStatus.Contains(transmissionStatus);
            if (isFinal)
            {
                return true;
            }
            if (uploadedAt.HasValue)
            {
                var daysSinceUpload = (DateTime.UtcNow - uploadedAt.Value).TotalDays;
                //CTS only keep file for 7 days, so if it's older than that, we consider it final
                if (daysSinceUpload > 7) return true;
            }
            return false;
        }

        /*
        private static bool AllowConditionalRegeneration(CTSUploadStatus? uploadStatus, string? transmissionStatus)
        {
            var errorStatus = new List<string>
            {
                CtsTransmissionStatus.RC002.ToString(),
                CtsTransmissionStatus.RC003.ToString(),
                CtsTransmissionStatus.RC004.ToString(),
                CtsTransmissionStatus.RC008.ToString(),
                CtsTransmissionStatus.RC010.ToString(),
                CtsTransmissionStatus.RC011.ToString(),
                CtsTransmissionStatus.RC012.ToString(),
                CtsTransmissionStatus.RC013.ToString(),
                CtsTransmissionStatus.RC014.ToString(),
                CtsTransmissionStatus.RC015.ToString(),
                CtsTransmissionStatus.RC018.ToString(),
                CtsTransmissionStatus.RC019.ToString(),
                CtsTransmissionStatus.RC025.ToString(),
                CtsTransmissionStatus.RC026.ToString(),
                CtsTransmissionStatus.RC027.ToString(),
                CtsTransmissionStatus.RC032.ToString(),
                CtsTransmissionStatus.RC999.ToString(),
            };
            // Allow regeneration only if the transmission status is not final
            return uploadStatus == CTSUploadStatus.Uploaded &&
                   (string.IsNullOrWhiteSpace(transmissionStatus) ||
                    errorStatus.Contains(transmissionStatus));
        }
        */
        public static bool ShouldRefreshTransmissionStatus(CtsPackageRequest packageRequest)
        {
            var currentStatus = packageRequest.UploadStatus ?? CTSUploadStatus.GenerationInProgress;
            if (currentStatus == CTSUploadStatus.Uploaded &&
                (string.IsNullOrWhiteSpace(packageRequest.TransmissionStatus) ||
                !IsTransmissionFinal(packageRequest)))
            {
                return true;
            }

            return false;
        }

        
        public static StateTransitionResult PerformActionAsync(CtsPackageRequest packageRequest, DataPacketAction action)
        {
            if (!CanPerformAction(packageRequest, action))
            {
                return new StateTransitionResult
                {
                    IsSuccess = false,
                    Message = $"Action {action} is not available for current state {packageRequest.UploadStatus}",
                    NewState = packageRequest.UploadStatus == CTSUploadStatus.GenerationInProgress ? null : packageRequest.UploadStatus,
                    AvailableActions = GetAvailableActions(packageRequest.UploadStatus, packageRequest.TransmissionStatus, packageRequest.UploadedAt)
                };
            }

            try
            {
                switch (action)
                {
                    case DataPacketAction.Upload:
                        return TriggerEvent(packageRequest, DataPacketEvent.StartUpload);

                    case DataPacketAction.Regenerate:
                        return TriggerEvent(packageRequest, DataPacketEvent.StartRegeneration);

                    case DataPacketAction.MarkDoNotUpload:
                        return TriggerEvent(packageRequest, DataPacketEvent.MarkAsDoNotUpload);

                    case DataPacketAction.UnmarkDoNotUpload:
                        return TriggerEvent(packageRequest, DataPacketEvent.UnmarkAsDoNotUpload);
 
                    default:
                        return new StateTransitionResult
                        {
                            IsSuccess = false,
                            Message = $"Action {action} is not implemented",
                            NewState = packageRequest.UploadStatus,
                            AvailableActions = GetAvailableActions(packageRequest.UploadStatus, packageRequest.TransmissionStatus, packageRequest.UploadedAt)
                        };
                }
            }
            catch (Exception ex)
            {
                return new StateTransitionResult
                {
                    IsSuccess = false,
                    Message = $"Error performing action {action}: {ex.Message}",
                    NewState = packageRequest.UploadStatus,
                    AvailableActions = GetAvailableActions(packageRequest.UploadStatus, packageRequest.TransmissionStatus, packageRequest.UploadedAt)
                };
            }
        }
    }
}
