import type { AuditedEntityDto, EntityDto, PagedAndSortedResultRequestDto } from '@abp/ng.core';
import type { ExchangeReason } from '../../../../shared/constants/information-exchanges/exchange-reason.enum';
import type { CTSUploadStatus } from '../../../enums/ctsupload-status.enum';
import type { DataPacketAction } from '../../../constants/data-packet-action.enum';

export interface CreateCtsPackageRequestDto extends EntityDto<string> {
  essInformationXmlId?: string;
  ctsPackageFileName?: string;
  receiverCountryCode?: string;
  fiscalYear: number;
  exchangeReason?: ExchangeReason;
  xmlPayload?: string;
  xmlPayloadUrl?: string;
  packageZipUrl?: string;
  fileCreatedAt?: string;
  uploadedAt?: string;
  uploadAttempts: number;
  uploadStatus?: CTSUploadStatus;
  eligibleCheckCtsStatus?: boolean;
  transmissionStatus?: string;
  isExcludeCtsUpload: boolean;
  statusUpdatedAt?: string;
  hasExchangeRecords: boolean;
  transmissionStatusDesc?: string;
  ctsTransmissionId?: string;
  transmissionStatusLastCheckedUtc?: string;
  tenantId?: string;
  messageRefId?: string;
}

export interface CtsPackageCommentDto extends AuditedEntityDto<string> {
  ctsPackageId?: string;
  comment?: string;
  ctsUploadStatus?: CTSUploadStatus;
  transmissionStatus?: string;
  ctsUploadStatusDesc?: string;
  creatorName?: string;
}

export interface CtsPackageRequestDto extends EntityDto<string> {
  exchangeReason?: ExchangeReason;
  ctsPackageId?: string;
  dataPacket?: string;
  financialPeriodEndYear: number;
  fileCreationDate?: string;
  receivingCountry?: string;
  ctsUploadStatus?: CTSUploadStatus;
  uploadedAt?: string;
  ctsTransmissionStatus?: string;
  viewExchangeRecords: boolean;
  viewComments: CtsPackageCommentDto[];
  regeneratePacket: boolean;
  ctsUpload: boolean;
  excludeFromCtsUpload: boolean;
  allowedActions: DataPacketAction[];
}

export interface CtsPackageRequestSummaryDto {
  totalNotUploaded: number;
  totalReadyForUpload: number;
  totalFailedUpload: number;
  totalUploadedToCTS: number;
  totalNotEnrolled: number;
  year?: string;
}

export interface GetCtsPackageRequestDto extends PagedAndSortedResultRequestDto {
  financialEndYear?: string;
  exchangeReason?: ExchangeReason;
  receivingCountry?: string;
  ctsUploadStatus?: CTSUploadStatus;
}
