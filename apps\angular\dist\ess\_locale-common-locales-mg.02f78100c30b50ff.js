"use strict";(self.webpackChunkess=self.webpackChunkess||[]).push([[42393],{67798:(n,s,l)=>{l.r(s),l.d(s,{default:()=>o});const a=void 0,o=["mg",[["AM","PM"],a,a],a,[["A","A","T","A","A","Z","A"],["Alah","Alats","Tal","Alar","Alak","Zom","Asab"],["Alahady","Alatsinainy","Talata","Alarobia","Alakamisy","Zoma","Asabotsy"],["<PERSON>ah","<PERSON><PERSON>","<PERSON>l","Alar","Alak","Zom","<PERSON>ab"]],a,[["J","F","M","A","M","J","J","A","S","O","N","<PERSON>"],["<PERSON>","<PERSON>","<PERSON>","<PERSON>","<PERSON><PERSON>","<PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON>","Ok<PERSON>","Nov","<PERSON>"],["<PERSON><PERSON><PERSON>","<PERSON>roary","<PERSON><PERSON>","Aprily","Mey","<PERSON>a","Jolay","Aogositra","Septambra","Oktobra","Novambra","Desambra"]],a,[["BC","AD"],a,["Alohan\u2019i JK","Aorian\u2019i JK"]],1,[6,0],["y-MM-dd","y MMM d","d MMMM y","EEEE d MMMM y"],["HH:mm","HH:mm:ss","HH:mm:ss z","HH:mm:ss zzzz"],["{1} {0}",a,a,a],[".",",",";","%","+","-","E","\xd7","\u2030","\u221e","NaN",":"],["#,##0.###","#,##0%","\xa4\xa0#,##0.00","#E0"],"MGA","Ar","Ariary",{JPY:["JP\xa5","\xa5"],MGA:["Ar"],USD:["US$","$"]},"ltr",function r(M){const A=M;return A===Math.floor(A)&&A>=0&&A<=1?1:5}]}}]);