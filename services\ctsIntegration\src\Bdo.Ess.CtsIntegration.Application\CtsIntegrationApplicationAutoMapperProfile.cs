using AutoMapper;
using Bdo.Ess.CtsIntegration.Application.Contracts.CtsPackageRequests;
using Bdo.Ess.CtsIntegration.BahamasCtsSettings;
using Bdo.Ess.CtsIntegration.Certificate;
using Bdo.Ess.CtsIntegration.CtsPackageRequests;
using Bdo.Ess.CtsIntegration.Entities;
using System;
using Volo.Abp.AutoMapper;

namespace Bdo.Ess.CtsIntegration;

public class CtsIntegrationApplicationAutoMapperProfile : Profile
{
    public CtsIntegrationApplicationAutoMapperProfile()
    {
        /* You can configure your AutoMapper mapping configuration here.
        * Alternatively, you can split your mapping configurations
        * into multiple profile classes for a better organization. */

        CreateMap<BahamasCertificate, BahamasCertificateDto>();

        CreateMap<CtsPackageRequest, CtsPackageRequestDto>()
            .ForMember(dest => dest.Id, op => op.MapFrom(src => src.Id))
            .ForMember(dest => dest.ExchangeReason, op => op.MapFrom(src => src.ExchangeReason))
            .ForMember(dest => dest.CtsPackageId, op => op.MapFrom(src => src.Id))
            .ForMember(dest => dest.DataPacket, op => op.MapFrom(src => src.CtsPackageFileName))
            .ForMember(dest => dest.FinancialPeriodEndYear, op => op.MapFrom(src => src.FiscalYear))
            .ForMember(dest => dest.FileCreationDate, op => op.MapFrom(src => src.FileCreatedAt))
            .ForMember(dest => dest.ReceivingCountry, op => op.MapFrom(src => src.ReceiverCountryCode))
            .ForMember(dest => dest.CtsUploadStatus, op => op.MapFrom(src => src.UploadStatus))
            .ForMember(dest => dest.UploadedAt, op => op.MapFrom(src => src.UploadedAt))
            .ForMember(dest => dest.CtsTransmissionStatus, op => op.MapFrom(src => src.TransmissionStatus))
            .ForMember(dest => dest.ViewExchangeRecords, op => op.Ignore())
            .ForMember(dest => dest.ViewComments, op => op.Ignore())
            .ForMember(dest => dest.RegeneratePacket, op => op.Ignore())
            .ForMember(dest => dest.CtsUpload, op => op.Ignore())
            .ForMember(dest => dest.AllowedActions, op => op.Ignore())
            .ForMember(dest => dest.ExcludeFromCtsUpload, op => op.MapFrom(src => src.HasExchangeRecords));

        //Mannual create instance, don't need map
        //CreateMap<CtsPackageComment, CtsPackageCommentDto>().ReverseMap();

        CreateMap<CreateCtsPackageRequestDto, CtsPackageRequest>()
            .Ignore(x=>x.ProcessInfo)
            .IgnoreFullAuditedObjectProperties();

        CreateMap<CtsPackageRequest, CtsPackageRequestDataDto>();

        CreateMap<CountryCertificate, CountryCertificateDto>().ReverseMap();

        // BahamasCtsSetting mappings
        CreateMap<Entities.BahamasCtsSetting, BahamasCtsSettingDto>();
        CreateMap<CreateBahamasCtsSettingDto, BahamasCtsSetting>()
            .Ignore(x => x.Id)
            .Ignore(x => x.SftpSSHKey)
            .Ignore(x => x.SystemUserPasswordUpdatedAt)
            .Ignore(x => x.SftpSSHKeyUpdatedAt)
            .Ignore(x => x.CtsPublicCertificate)
            .Ignore(x => x.CtsPublicCertificateUpdatedAt)
            .IgnoreFullAuditedObjectProperties();
        CreateMap<UpdateBahamasCtsSettingDto, BahamasCtsSetting>()
            .Ignore(x=>x.Id)
            .Ignore(x => x.SftpSSHKey)
            .Ignore(x => x.SystemUserPasswordUpdatedAt)
            .Ignore(x => x.SftpSSHKeyUpdatedAt)
            .Ignore(x => x.CtsPublicCertificate)
            .Ignore(x => x.CtsPublicCertificateUpdatedAt)
            .IgnoreFullAuditedObjectProperties();

        CreateMap<BahamasCtsSetting, BahamasCtsSetting>();
        CreateMap<CtsPackageRequest, CtsPackageRequest>();
        CreateMap<CountryCertificate, CountryCertificate>();
        CreateMap<BahamasCertificate, BahamasCertificate>();

    }
}
